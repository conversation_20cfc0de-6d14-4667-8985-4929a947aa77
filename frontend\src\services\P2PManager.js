import { io } from 'socket.io-client';
import { encryptRoomId, decryptRoomId } from '../utils/roomEncryption';

/**
 * Simple EventEmitter implementation for browser compatibility
 */
class SimpleEventEmitter {
  constructor() {
    this.listeners = new Map();
  }

  on(event, listener) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    this.listeners.get(event).add(listener);
  }

  off(event, listener) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).delete(listener);
    }
  }

  emit(event, ...args) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(listener => {
        try {
          listener(...args);
        } catch (error) {
          console.error(`Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  removeAllListeners(event) {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }
}

/**
 * P2P Manager for decentralized room management using WebRTC
 * Handles networking and syncing with a centralized GameManager
 */
class P2PManager extends SimpleEventEmitter {
  constructor() {
    super();
    this.peers = new Map(); // peerId -> { connection, dataChannel, stream, ... }
    this.peerId = this.generatePeerId();
    this.localUserData = null;
    this.isOwner = false;
    this.roomId = null;
    this.encryptedRoomId = null;
    this.localStream = null;
    this.gameManager = null; // Reference to GameManager
    this.roomState = {
      players: [],
      settings: {
        players: 8,
        language: 'English',
        drawTime: 80,
        rounds: 3,
        gameMode: 'Normal',
        wordCount: 3,
        hints: 2,
        customWords: '',
        useCustomWordsOnly: false
      },
      scores: new Map() // peerId -> score
    };

    // WebRTC configuration with public STUN servers
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' }
      ]
    };

    // Initialize Socket.IO connection
    this.socket = io('http://************:3000', {
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    this.setupSocketListeners();
  }

  /**
   * Set the GameManager reference
   */
  setGameManager(gameManager) {
    this.gameManager = gameManager;
    // console.log('🔗 GameManager linked to P2PManager');
  }

  /**
   * Handle ownership transfer
   */
  transferOwnership(newOwnerId) {
    const wasOwner = this.isOwner;
    this.isOwner = (newOwnerId === this.peerId);

    // console.log('👑 Ownership transfer:', {
    //   newOwner: newOwnerId,
    //   isNowOwner: this.isOwner,
    //   wasOwner: wasOwner
    // });

    // Handle GameManager activation/deactivation
    if (this.gameManager) {
      if (this.isOwner && !wasOwner) {
        // Becoming owner - activate GameManager
        // console.log('🎮 Activating GameManager - I am now the owner');
        this.gameManager.activate();
      } else if (!this.isOwner && wasOwner) {
        // Losing ownership - deactivate GameManager
        // console.log('🎮 Deactivating GameManager - I am no longer the owner');
        this.gameManager.deactivate();
      }
    }

    this.emit('ownershipChanged', {
      isOwner: this.isOwner,
      newOwner: newOwnerId,
      wasOwner: wasOwner
    });
  }

  setupSocketListeners() {
    // Room creation response
    this.socket.on('roomCreated', ({ success, roomId, error }) => {
      if (success) {
        ////console.log(`Room ${roomId} created successfully`);
        this.roomState.players = [this.localUserData];
        this.emit('roomStateUpdated', this.roomState);
      } else {
        console.error('Room creation failed:', error);
        this.emit('error', error);
      }
    });

    // Room joining response
    this.socket.on('roomJoined', async ({ success, roomId, peers, isOwner, settings, error }) => {
      if (success) {
        // console.log(`�� Joined room ${roomId} with ${peers.length} peers, settings:`, settings);
        this.isOwner = isOwner;

        // Update room state with existing peers and settings
        this.roomState.players = peers.map(p => p.userData).concat([this.localUserData]);
        if (settings) {
          this.roomState.settings = settings;
          this.emit('settingsChanged', settings);
        }
        this.emit('roomStateUpdated', this.roomState);

        // Connect to all existing peers
        for (const peer of peers) {
          await this.connectToPeer(peer.peerId);
        }
      } else {
        console.error('Failed to join room:', error);
        this.emit('error', error);
      }
    });

    // New peer joined
    this.socket.on('peerJoined', async ({ peerId, userData }) => {
      //console.log(`New peer joined: ${peerId}`, userData);

      // Check if peer already exists to avoid duplicates
      const existingPeerIndex = this.roomState.players.findIndex(p => p.peerId === peerId);
      if (existingPeerIndex === -1) {
        // Add new peer to room state
        this.roomState.players.push(userData);
        //console.log('Updated room state with new peer:', this.roomState.players);
        this.emit('roomStateUpdated', this.roomState);
      }

      // Connect to the new peer
      await this.connectToPeer(peerId);
    });

    // Peer left
    this.socket.on('peerLeft', ({ peerId }) => {
      ////console.log(`Peer left: ${peerId}`);
      this.handlePeerDisconnection(peerId);
      // Update room state
      this.roomState.players = this.roomState.players.filter(p => p.peerId !== peerId);
      this.emit('roomStateUpdated', this.roomState);
    });

    // Ownership transferred
    this.socket.on('ownershipTransferred', ({ newOwner }) => {
      // console.log(`👑 Room ownership transferred to: ${newOwner}`);
      this.transferOwnership(newOwner);
    });

    // Room expired
    this.socket.on('roomExpired', () => {
      ////console.log('Room has expired');
      this.emit('roomExpired');
      this.leaveRoom();
    });

    // WebRTC Signaling
    this.socket.on('signal', async ({ fromPeerId, signal }) => {
      ////console.log(`Received signal from ${fromPeerId}:`, signal.type || 'ICE candidate');
      await this.handleSignal({ from: fromPeerId, signal });
    });

    // Settings update
    this.socket.on('settingsUpdated', ({ settings }) => {
      // console.log('📡 Received settings update from backend:', settings);
      this.roomState.settings = settings;
      this.emit('settingsChanged', settings);
      this.emit('roomStateUpdated', this.roomState);
    });

    // Score update
    this.socket.on('scoreUpdated', ({ peerId, score }) => {
      this.roomState.scores.set(peerId, score);
      this.emit('roomStateUpdated', this.roomState);
    });
  }

  async createRoom(userData) {
    this.localUserData = { ...userData, peerId: this.peerId, joinTime: Date.now() };
    this.isOwner = true;

    const roomId = this.generateRoomId();
    const encryptedRoomId = encryptRoomId(roomId);

    this.socket.emit('createRoom', {
      roomId: roomId,
      userData: this.localUserData
    });

    return new Promise((resolve, reject) => {
      this.socket.once('roomCreated', ({ success, roomId, error }) => {
        if (success) {
          this.roomId = roomId;
          this.encryptedRoomId = encryptedRoomId;
          //console.log("encryptedRoomId", this.encryptedRoomId);
          resolve({
            roomId: roomId,
            encryptedRoomId: encryptedRoomId
          });
        } else {
          reject(new Error(error || 'Failed to create room'));
        }
      });
    });
  }

  async joinRoom(roomId, userData) {
    ////console.log("Joining room with ID:", roomId);
    this.roomId = roomId;
    this.localUserData = { ...userData, peerId: this.peerId, joinTime: Date.now() };

    this.socket.emit('joinRoom', {
      roomId: roomId,
      userData: this.localUserData
    });

    return new Promise((resolve, reject) => {
      this.socket.once('roomJoined', ({ success, error }) => {
        if (success) {
          this.encryptedRoomId = encryptRoomId(roomId);
          resolve();
        } else {
          reject(new Error(error || 'Failed to join room'));
        }
      });

      this.socket.once('joinRoomError', ({ message }) => {
        reject(new Error(message));
      });
    });
  }

  async connectToPeer(peerId) {
    //console.log(`Initiating connection to peer ${peerId}`);

    // Check if we already have a connection to this peer
    let peer = this.peers.get(peerId);
    if (peer && peer.connection.connectionState !== 'closed' && peer.connection.connectionState !== 'failed') {
      //console.log(`Already have connection to peer ${peerId}, state: ${peer.connection.connectionState}`);
      return peer;
    }

    // Create new peer connection
    peer = await this.createPeerConnection(peerId);

    // Create and setup data channel
    const dataChannel = peer.connection.createDataChannel('data');
    this.setupDataChannel(peerId, dataChannel);

    // Only create offer if this peer has a "lower" ID to avoid simultaneous offers
    // This prevents the race condition where both peers try to create offers
    const shouldCreateOffer = this.peerId < peerId;

    if (shouldCreateOffer) {
      //console.log(`Creating offer for peer ${peerId} (this peer has lower ID)`);
      // Create and send offer
      const offer = await peer.connection.createOffer();
      await peer.connection.setLocalDescription(offer);
      //console.log(`Sending offer to peer ${peerId}`);
      this.sendSignal(peerId, offer);
    } else {
      //console.log(`Waiting for offer from peer ${peerId} (this peer has higher ID)`);
    }

    return peer;
  }

  sendSignal(targetPeerId, signal) {
    this.socket.emit('signal', {
      targetPeerId,
      signal
    });
  }

  /**
   * Create a WebRTC peer connection
   */
  async createPeerConnection(peerId) {
    //console.log(`Creating peer connection for ${peerId}`);

    // Clean up any existing connection for this peer
    const existingPeer = this.peers.get(peerId);
    if (existingPeer) {
      //console.log(`Cleaning up existing connection for ${peerId}`);
      if (existingPeer.connection) {
        existingPeer.connection.close();
      }
      if (existingPeer.dataChannel) {
        existingPeer.dataChannel.close();
      }
    }

    const connection = new RTCPeerConnection(this.rtcConfig);
    const peer = {
      connection,
      dataChannel: null,
      stream: null,
      iceCandidateQueue: []
    };

    this.peers.set(peerId, peer);

    // Add local stream if available
    this.addStreamToPeer(peerId, peer);

    // Handle incoming streams
    connection.ontrack = (event) => {
      // console.log(`📹 Received stream from ${peerId}:`, {
      //   streamId: event.streams[0]?.id,
      //   tracks: event.streams[0]?.getTracks()?.length,
      //   videoTracks: event.streams[0]?.getVideoTracks()?.length,
      //   audioTracks: event.streams[0]?.getAudioTracks()?.length
      // });
      peer.stream = event.streams[0];
      this.emit('streamReceived', { peerId, stream: event.streams[0] });
    };

    // Handle ICE candidates
    connection.onicecandidate = (event) => {
      if (event.candidate) {
        ////console.log(`Sending ICE candidate to ${peerId}`);
        this.sendSignal(peerId, event.candidate);
      }
    };

    // Handle connection state changes
    connection.onconnectionstatechange = () => {
      // console.log(`🔗 Connection state with ${peerId}: ${connection.connectionState}`);
      if (connection.connectionState === 'connected') {
        // console.log(`✅ Successfully connected to peer ${peerId}`);

        // Exchange user information once connected
        this.sendToPeer(peerId, {
          type: 'userInfo',
          userData: this.localUserData
        });

        // If we have a local stream but haven't added it yet, add it now
        if (this.localStream) {
          // console.log(`📤 Adding local stream to newly connected peer ${peerId}`);
          this.addStreamToPeer(peerId, peer);
        } else {
          // console.warn(`⚠️ No local stream available to add to peer ${peerId}`);
        }

        this.emit('peerConnected', peerId);
      } else if (connection.connectionState === 'failed' ||
                 connection.connectionState === 'closed' ||
                 connection.connectionState === 'disconnected') {
        // console.log(`❌ Connection with peer ${peerId} ${connection.connectionState}`);
        this.handlePeerDisconnection(peerId);
      }
    };

    // Handle incoming data channels
    connection.ondatachannel = (event) => {
      ////console.log(`Received data channel from ${peerId}`);
      this.setupDataChannel(peerId, event.channel);
    };

    return peer;
  }

  /**
   * Setup data channel for peer communication
   */
  setupDataChannel(peerId, channel) {
    const peer = this.peers.get(peerId);
    if (!peer) return;

    peer.dataChannel = channel;
    
    channel.onopen = () => {
      // console.log(`Data channel with ${peerId} opened`);

      // Send user information when data channel opens
      this.sendToPeer(peerId, {
        type: 'userInfo',
        userData: this.localUserData
      });
    };

    channel.onclose = () => {
      ////console.log(`Data channel with ${peerId} closed`);
    };

    channel.onerror = (error) => {
      console.error(`Data channel error with ${peerId}:`, error);
    };

    channel.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handlePeerMessage(peerId, data);
      } catch (error) {
        console.error('Error parsing peer message:', error);
      }
    };
  }

  /**
   * Handle messages from peers
   */
  handlePeerMessage(peerId, data) {
    const peer = this.peers.get(peerId);
    if (!peer) return;

    switch (data.type) {
      case 'userInfo':
        // console.log(`Received user info from ${peerId}:`, data.userData);
        peer.userData = data.userData;
        peer.joinTime = data.userData.joinTime;

        // Check if this peer is already in room state to avoid duplicates
        const existingPeerIndex = this.roomState.players.findIndex(p => p.peerId === peerId);
        if (existingPeerIndex === -1) {
          this.roomState.players.push(data.userData);
          // console.log('Added peer to room state via userInfo:', data.userData);
        } else {
          // Update existing peer data
          this.roomState.players[existingPeerIndex] = data.userData;
          // console.log('Updated existing peer in room state:', data.userData);
        }

        this.updateRoomState();
        this.emit('peerUserInfo', { peerId, userData: data.userData });
        break;

      case 'roomStateUpdate':
        this.handleRoomStateUpdate(data.roomState, peerId);
        break;

      case 'settingsChange':
        ////console.log('Received settings change from peer:', peerId, data.settings);
        if (this.canPeerChangeSettings(peerId)) {
          ////console.log('Applying settings change from owner');
          this.roomState.settings = { ...this.roomState.settings, ...data.settings };
          this.emit('settingsChanged', this.roomState.settings);
        } else {
          ////console.log('Ignoring settings change from non-owner');
        }
        break;

      case 'gameStateSync':
        this.handleGameStateSync(data.gameState, peerId);
        break;

      case 'drawingData':
        this.emit('drawingData', { peerId, data: data.drawingData });
        break;

      case 'cameraFrame':
        this.emit('cameraFrame', { peerId, frame: data.frame });
        break;

      case 'gameAction':
        this.handleGameAction(data.action, data.payload, peerId);
        break;
    }
  }

  /**
   * Send message to specific peer
   */
  sendToPeer(peerId, data) {
    const peer = this.peers.get(peerId);
    if (peer && peer.dataChannel && peer.dataChannel.readyState === 'open') {
      peer.dataChannel.send(JSON.stringify(data));
    }
  }

  /**
   * Broadcast message to all connected peers
   */
  broadcast(data) {
    this.peers.forEach((peer, peerId) => {
      this.sendToPeer(peerId, data);
    });
  }

  /**
   * Handle peer disconnection
   */
  handlePeerDisconnection(peerId) {
    const peer = this.peers.get(peerId);
    if (peer) {
      if (peer.stream) {
        peer.stream.getTracks().forEach(track => track.stop());
      }
      if (peer.connection) {
        peer.connection.close();
      }
      if (peer.dataChannel) {
        peer.dataChannel.close();
      }
      this.peers.delete(peerId);
    }

    // Update room state
    this.roomState.players = this.roomState.players.filter(p => p.peerId !== peerId);
    this.roomState.scores.delete(peerId);

    // Note: Ownership transfer is handled by the server, not locally
    // The server will send an 'ownershipTransferred' event if needed

    // Notify GameManager about disconnection (only if we're still the active manager)
    if (this.gameManager && this.gameManager.isActiveManager()) {
      this.gameManager.handlePlayerDisconnection(peerId);
    }

    this.emit('roomStateUpdated', this.roomState);
    this.emit('peerDisconnected', peerId);
  }

  /**
   * Broadcast game state to all peers (only called by active GameManager)
   */
  broadcastGameState(gameState) {
    if (!this.isOwner) {
      console.warn('🎮 Non-owner trying to broadcast game state');
      return;
    }

    // console.log('🎮 Broadcasting game state to all peers');
    this.broadcast({
      type: 'gameStateSync',
      gameState: gameState
    });
  }

  /**
   * Send game action to authoritative GameManager
   */
  sendGameAction(action, payload) {
    // console.log('🎮 Sending game action:', action);

    // If we're the owner, handle locally
    if (this.isOwner && this.gameManager && this.gameManager.isActiveManager()) {
      this.handleGameAction(action, payload, this.peerId);
      return;
    }

    // Otherwise, send to owner
    const owner = this.getOwnerPeer();
    if (owner) {
      this.sendToPeer(owner.peerId, {
        type: 'gameAction',
        action: action,
        payload: payload
      });
    }
  }

  /**
   * Get the current owner peer
   */
  getOwnerPeer() {
    const players = this.roomState.players.sort((a, b) => a.joinTime - b.joinTime);
    return players.length > 0 ? players[0] : null;
  }

  /**
   * Update room settings (owner only)
   */
  updateSettings(newSettings) {
    if (!this.isOwner) return;
    
    this.roomState.settings = { ...this.roomState.settings, ...newSettings };
    
    // Emit to all peers via server to ensure consistency
    this.socket.emit('updateSettings', {
      roomId: this.roomId,
      settings: this.roomState.settings
    });
    
    // Also update local state
    this.emit('roomStateUpdated', this.roomState);
  }

  /**
   * Check if peer can change settings
   */
  canPeerChangeSettings(peerId) {
    return this.roomState.ownerId === peerId;
  }

  /**
   * Handle room state updates from peers
   */
  handleRoomStateUpdate(newRoomState, fromPeerId) {
    // Simple conflict resolution: accept updates from owner or newer timestamps
    const fromPeer = this.peers.get(fromPeerId);
    if (!fromPeer) return;

    if (fromPeerId === this.roomState.ownerId ||
        newRoomState.timestamp > (this.roomState.timestamp || 0)) {

      // Preserve the scores Map when updating room state
      const currentScores = this.roomState.scores;
      this.roomState = { ...this.roomState, ...newRoomState };

      // Ensure scores remains a Map
      if (!(this.roomState.scores instanceof Map)) {
        this.roomState.scores = currentScores || new Map();
      }

      this.emit('roomStateUpdated', this.roomState);
    }
  }

  /**
   * Handle game state sync from authoritative GameManager
   */
  handleGameStateSync(gameState, fromPeerId) {
    // Only accept game state from the current owner
    if (!this.isOwnerPeer(fromPeerId)) {
      console.warn('🎮 Ignoring game state sync from non-owner:', fromPeerId);
      return;
    }

    // console.log('🎮 Received game state sync from owner:', fromPeerId);

    // Forward to GameManager for syncing
    if (this.gameManager && !this.gameManager.isActiveManager()) {
      this.gameManager.syncGameState(gameState);
    }

    // Emit for UI updates
    this.emit('gameStateUpdated', gameState);
  }

  /**
   * Handle game actions from peers
   */
  handleGameAction(action, payload, fromPeerId) {
    // console.log('🎮 Received game action:', action, 'from:', fromPeerId);

    // Forward to GameManager if this is the authoritative instance
    if (this.gameManager && this.gameManager.isActiveManager()) {
      switch (action) {
        case 'submitGuess':
          this.gameManager.submitGuess(fromPeerId, payload.guess);
          break;
        case 'selectWord':
          this.gameManager.selectWord(fromPeerId, payload.word);
          break;
        case 'drawingData':
          this.gameManager.handleDrawingData(payload);
          break;
        default:
          console.warn('🎮 Unknown game action:', action);
      }
    }
  }

  /**
   * Check if a peer is the current owner
   */
  isOwnerPeer(peerId) {
    // Find the oldest player (owner)
    const players = this.roomState.players.sort((a, b) => a.joinTime - b.joinTime);
    return players.length > 0 && players[0].peerId === peerId;
  }

  /**
   * Update room state and notify peers
   */
  updateRoomState() {
    // Update players list with connected peers
    const connectedPlayers = [this.localUserData];

    this.peers.forEach((peer, peerId) => {
      ////console.log(`Checking peer ${peerId}:`, { hasUserData: !!peer.userData, userData: peer.userData });
      if (peer.userData) {
        connectedPlayers.push(peer.userData);
      }
    });

    ////console.log('Connected players before update:', connectedPlayers);
    this.roomState.players = connectedPlayers.sort((a, b) => a.joinTime - b.joinTime);
    this.roomState.timestamp = Date.now();

    ////console.log('Updated room state:', this.roomState);

    // Broadcast updated state
    this.broadcast({
      type: 'roomStateUpdate',
      roomState: this.roomState
    });

    this.emit('roomStateUpdated', this.roomState);
  }

  /**
   * Store room info in localStorage for peer discovery
   */
  storeRoomInfo(roomInfo = null) {
    const info = roomInfo || {
      roomId: this.roomId,
      ownerId: this.roomState.ownerId,
      peers: [this.peerId],
      createdAt: Date.now()
    };

    localStorage.setItem(`room_${this.roomId}`, JSON.stringify(info));
  }

  /**
   * Get room info from localStorage
   */
  getRoomInfo(roomId) {
    const stored = localStorage.getItem(`room_${roomId}`);
    return stored ? JSON.parse(stored) : null;
  }

  /**
   * Update stored room info with current peers
   */
  updateStoredRoomInfo() {
    const roomInfo = this.getRoomInfo(this.roomId);
    if (roomInfo) {
      roomInfo.peers = [this.peerId, ...Array.from(this.peers.keys())];
      this.storeRoomInfo(roomInfo);
    }
  }

  /**
   * Start the game (owner only)
   */
  startGame() {
    if (!this.isOwner) return;

    this.roomState.gameState = 'playing';
    this.roomState.currentRound = 1;

    // Select first drawer (oldest player)
    const sortedPlayers = [...this.roomState.players].sort((a, b) => a.joinTime - b.joinTime);
    this.roomState.currentDrawer = sortedPlayers[0];

    this.broadcast({
      type: 'gameStateChange',
      gameState: 'playing',
      currentDrawer: this.roomState.currentDrawer
    });

    this.emit('gameStarted', this.roomState);
  }

  /**
   * Send drawing data to all peers
   */
  sendDrawingData(drawingData) {
    this.broadcast({
      type: 'drawingData',
      drawingData: drawingData
    });
  }

  /**
   * Send camera frame to all peers
   */
  sendCameraFrame(frameData) {
    this.broadcast({
      type: 'cameraFrame',
      frame: frameData
    });
  }

  /**
   * Get current room state
   */
  getRoomState() {
    return this.roomState;
  }

  /**
   * Get connected peers
   */
  getConnectedPeers() {
    return Array.from(this.peers.keys());
  }

  /**
   * Cleanup when leaving room
   */
  leaveRoom() {
    this.stopCamera();
    
    if (this.socket) {
      this.socket.disconnect();
    }
    
    // Close all peer connections
    for (const [peerId, peer] of this.peers.entries()) {
      if (peer.stream) {
        peer.stream.getTracks().forEach(track => track.stop());
      }
      if (peer.connection) {
        peer.connection.close();
      }
      if (peer.dataChannel) {
        peer.dataChannel.close();
      }
    }
    
    this.peers.clear();
    this.roomId = null;
    this.isOwner = false;
    this.emit('roomLeft');
  }

  generatePeerId() {
    return `peer_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  generateRoomId() {
    return Math.random().toString(36).substr(2, 8).toUpperCase();
  }

  async handleSignal(signalData) {
    const { from: fromPeerId, signal } = signalData;
    let peer = this.peers.get(fromPeerId);

    try {
      if (signal.type === 'offer') {
        // console.log(`Handling offer from ${fromPeerId}, current state:`, peer?.connection?.signalingState);

        // Create new peer connection for incoming offer if it doesn't exist
        if (!peer) {
          peer = await this.createPeerConnection(fromPeerId);
        }

        // Handle offer based on signaling state
        if (peer.connection.signalingState === 'stable') {
          // Normal case: we can accept the offer
          await peer.connection.setRemoteDescription(signal);

          // Create and send answer
          const answer = await peer.connection.createAnswer();
          await peer.connection.setLocalDescription(answer);
          // console.log(`Sending answer to ${fromPeerId}`);
          this.sendSignal(fromPeerId, answer);

        } else if (peer.connection.signalingState === 'have-local-offer') {
          // Collision case: both peers sent offers simultaneously
          // console.log(`Offer collision detected with ${fromPeerId}, resolving...`);

          // Use peer ID comparison to decide who should back down
          if (this.peerId > fromPeerId) {
            // This peer backs down: accept the remote offer
            // console.log(`Backing down from collision, accepting offer from ${fromPeerId}`);
            await peer.connection.setRemoteDescription(signal);

            // Create and send answer
            const answer = await peer.connection.createAnswer();
            await peer.connection.setLocalDescription(answer);
            // console.log(`Sending answer to ${fromPeerId} after collision resolution`);
            this.sendSignal(fromPeerId, answer);
          } else {
            // Remote peer should back down: ignore their offer
            // console.log(`Remote peer ${fromPeerId} should back down from collision`);
          }
        } else {
          // console.log(`Cannot handle offer from ${fromPeerId}, wrong state: ${peer.connection.signalingState}`);
        }

        // Process any queued ICE candidates after setting remote description
        if (peer.connection.remoteDescription && peer.iceCandidateQueue && peer.iceCandidateQueue.length > 0) {
          // console.log(`Processing ${peer.iceCandidateQueue.length} queued ICE candidates for ${fromPeerId}`);
          for (const candidate of peer.iceCandidateQueue) {
            await peer.connection.addIceCandidate(candidate);
          }
          peer.iceCandidateQueue = [];
        }

      } else if (signal.type === 'answer' && peer) {
        // console.log(`Handling answer from ${fromPeerId}, current state:`, peer.connection.signalingState);

        // Check if we can set remote description
        if (peer.connection.signalingState === 'have-local-offer') {
          await peer.connection.setRemoteDescription(signal);

          // Process any queued ICE candidates
          if (peer.iceCandidateQueue && peer.iceCandidateQueue.length > 0) {
            // console.log(`Processing ${peer.iceCandidateQueue.length} queued ICE candidates for ${fromPeerId}`);
            for (const candidate of peer.iceCandidateQueue) {
              await peer.connection.addIceCandidate(candidate);
            }
            peer.iceCandidateQueue = [];
          }
        } else {
          // console.log(`Cannot handle answer from ${fromPeerId}, wrong state: ${peer.connection.signalingState}`);
        }

      } else if (signal.candidate && peer) {
        // console.log(`Handling ICE candidate from ${fromPeerId}`);
        if (peer.connection.remoteDescription) {
          await peer.connection.addIceCandidate(signal);
        } else {
          // console.log(`Queueing ICE candidate (no remote description yet)`);
          if (!peer.iceCandidateQueue) {
            peer.iceCandidateQueue = [];
          }
          peer.iceCandidateQueue.push(signal);
        }
      }
    } catch (error) {
      console.error(`Error handling signal from ${fromPeerId}:`, error);
      console.error('Signal details:', signal);
      console.error('Peer connection state:', peer?.connection?.signalingState);
    }
  }

  async startCamera() {
    try {
      // console.log('Starting camera...');
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: false
      });

      // console.log('📷 Camera started successfully, adding to existing peers:', this.peers.size);

      // Add stream to all existing peer connections
      for (const [peerId, peer] of this.peers.entries()) {
        // console.log(`🔄 Adding stream to existing peer: ${peerId}`);
        this.addStreamToPeer(peerId, peer);

        // If connection is already established, renegotiate to include the stream
        if (peer.connection.connectionState === 'connected') {
          // console.log(`🔄 Renegotiating connection with ${peerId} to add stream`);
          try {
            const offer = await peer.connection.createOffer();
            await peer.connection.setLocalDescription(offer);
            this.sendSignal(peerId, offer);
          } catch (renegotiateError) {
            console.error(`Failed to renegotiate with ${peerId}:`, renegotiateError);
          }
        }
      }

      return this.localStream;
    } catch (error) {
      console.error('Error accessing camera:', error);
      throw error;
    }
  }

  stopCamera() {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }
  }

  addStreamToPeer(peerId, peer) {
    if (this.localStream && peer.connection) {
      // console.log(`📤 Adding local stream to peer ${peerId}`, {
      //   streamId: this.localStream.id,
      //   tracks: this.localStream.getTracks().length,
      //   videoTracks: this.localStream.getVideoTracks().length,
      //   connectionState: peer.connection.connectionState,
      //   signalingState: peer.connection.signalingState
      // });

      // Check if tracks are already added to avoid duplicates
      const senders = peer.connection.getSenders();
      const existingTracks = senders.map(sender => sender.track).filter(track => track);

      this.localStream.getTracks().forEach(track => {
        // Only add track if it's not already added
        if (!existingTracks.includes(track)) {
          // console.log(`➕ Adding track ${track.kind} to peer ${peerId}`, {
          //   trackId: track.id,
          //   enabled: track.enabled,
          //   readyState: track.readyState
          // });
          peer.connection.addTrack(track, this.localStream);
        } else {
          // console.log(`⏭️ Track ${track.kind} already exists for peer ${peerId}`);
        }
      });
    } else {
      console.warn(`❌ Cannot add stream to peer ${peerId}:`, {
        hasLocalStream: !!this.localStream,
        hasConnection: !!peer.connection,
        connectionState: peer.connection?.connectionState
      });
    }
  }

  updateScore(peerId, score) {
    this.roomState.scores.set(peerId, score);
    
    // Emit to all peers via server to ensure consistency
    this.socket.emit('updateScore', {
      roomId: this.roomId,
      peerId,
      score
    });
    
    // Also update local state
    this.emit('roomStateUpdated', this.roomState);
  }
}

export default P2PManager;
