/**
 * Test utility for word extraction functionality
 * Run this in browser console to test word loading
 */

import { 
  extractWordsFromCSV, 
  getRandomWord, 
  getRandomWords, 
  getWordStats,
  getWordsByCategory 
} from './wordExtractor.js';

/**
 * Test all word extraction functions
 */
export function testWordExtraction() {
  // console.log('🧪 Testing Word Extraction Functions...\n');

  try {
    // Test 1: Extract all words
    // console.log('📝 Test 1: Extracting all words from CSV...');
    const allWords = extractWordsFromCSV();
    // console.log(`✅ Loaded ${allWords.length} words`);
    // console.log('First 10 words:', allWords.slice(0, 10));
    // console.log('Last 10 words:', allWords.slice(-10));
    // console.log('');

    // Test 2: Get random word
    // console.log('🎲 Test 2: Getting random words...');
    for (let i = 0; i < 5; i++) {
      // console.log(`Random word ${i + 1}: "${getRandomWord()}"`);
    }
    // console.log('');

    // Test 3: Get multiple random words
    // console.log('🎯 Test 3: Getting multiple random words...');
    const randomWords = getRandomWords(5);
    // console.log('5 random words:', randomWords);
    // console.log('');

    // Test 4: Word statistics
    // console.log('📊 Test 4: Word statistics...');
    const stats = getWordStats();
    // console.log('Statistics:', stats);
    // console.log('');

    // Test 5: Category filtering (if implemented)
    // console.log('🏷️ Test 5: Category filtering...');
    const countries = getWordsByCategory('countries');
    // console.log(`Countries (${countries.length}):`, countries.slice(0, 10));
    
    const food = getWordsByCategory('food');
    // console.log(`Food (${food.length}):`, food.slice(0, 10));
    
    const emotions = getWordsByCategory('emotions');
    // console.log(`Emotions (${emotions.length}):`, emotions.slice(0, 10));
    // console.log('');

    // Test 6: Check for duplicates
    // console.log('🔍 Test 6: Checking for duplicates...');
    const wordCounts = {};
    allWords.forEach(word => {
      wordCounts[word] = (wordCounts[word] || 0) + 1;
    });
    
    const duplicates = Object.entries(wordCounts)
      .filter(([word, count]) => count > 1)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10);
    
    // console.log('Top 10 most frequent words:', duplicates);
    // console.log('');

    // Test 7: Word length analysis
    // console.log('📏 Test 7: Word length analysis...');
    const lengths = allWords.map(word => word.length);
    const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
    const minLength = Math.min(...lengths);
    const maxLength = Math.max(...lengths);
    
    // console.log(`Average length: ${avgLength.toFixed(2)} characters`);
    // console.log(`Shortest word: ${minLength} characters`);
    // console.log(`Longest word: ${maxLength} characters`);
    
    const shortestWords = allWords.filter(word => word.length === minLength);
    const longestWords = allWords.filter(word => word.length === maxLength);
    
    // console.log(`Shortest words (${shortestWords.length}):`, shortestWords.slice(0, 5));
    // console.log(`Longest words (${longestWords.length}):`, longestWords.slice(0, 5));
    // console.log('');

    // console.log('✅ All tests completed successfully!');
    
    return {
      success: true,
      totalWords: allWords.length,
      stats,
      duplicates: duplicates.length,
      avgLength
    };

  } catch (error) {
    // console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Quick test function for console use
 */
export function quickTest() {
  // console.log('🚀 Quick Word Extraction Test');
  
  const words = extractWordsFromCSV();
  // console.log(`📝 Loaded ${words.length} words`);
  
  const randomWord = getRandomWord();
  // console.log(`🎲 Random word: "${randomWord}"`);
  
  const stats = getWordStats();
  // console.log('📊 Stats:', stats);
  
  return { words: words.length, randomWord, stats };
}

/**
 * Test word extraction performance
 */
export function performanceTest() {
  // console.log('⚡ Performance Test');
  
  const iterations = 1000;
  
  // Test word extraction speed
  // console.time('Word Extraction');
  for (let i = 0; i < 10; i++) {
    extractWordsFromCSV();
  }
  // console.timeEnd('Word Extraction');
  
  // Test random word generation speed
  // console.time('Random Word Generation');
  for (let i = 0; i < iterations; i++) {
    getRandomWord();
  }
  // console.timeEnd('Random Word Generation');
  
  // Test multiple random words speed
  // console.time('Multiple Random Words');
  for (let i = 0; i < 100; i++) {
    getRandomWords(5);
  }
  // console.timeEnd('Multiple Random Words');
  
  // console.log('✅ Performance test completed');
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  window.testWordExtraction = testWordExtraction;
  window.quickTestWords = quickTest;
  window.performanceTestWords = performanceTest;
  
  // console.log('🔧 Word extraction test functions available:');
  // console.log('- testWordExtraction() - Full test suite');
  // console.log('- quickTestWords() - Quick test');
  // console.log('- performanceTestWords() - Performance test');
}

export default {
  testWordExtraction,
  quickTest,
  performanceTest
};
