/* Camera Component Styles */
.camera-container {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  background: #1a1a1a;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 100%;
  aspect-ratio: 4/3;
  /* Enable container queries for responsive font sizing */
  container-type: inline-size;
  /* CSS custom properties for dynamic font sizing fallback */
  --dynamic-font-size: 12px;
  --current-width: 200px;
}

.camera-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scaleX(-1); /* Mirror the video */
  background: #000;
  border-radius: 12px;
}

.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: min(1.5vw, 4px);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none;
}

/* Fallback font sizing using CSS custom properties */
.camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
  font-size: var(--dynamic-font-size, 12px);
}

/* Dynamic font sizing based on container width using container queries */
/* These will override the custom property fallback in supporting browsers */
@container (min-width: 50px) {
  .camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
    font-size: 6px;
  }
}

@container (min-width: 80px) {
  .camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
    font-size: 8px;
  }
}

@container (min-width: 120px) {
  .camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
    font-size: 10px;
  }
}

@container (min-width: 160px) {
  .camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
    font-size: 12px;
  }
}

@container (min-width: 200px) {
  .camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
    font-size: 14px;
  }
}

@container (min-width: 250px) {
  .camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
    font-size: 16px;
  }
}

@container (min-width: 300px) {
  .camera-top, .camera-bottom, .camera-name, .camera-rank, .camera-points, .camera-crown {
    font-size: 18px;
  }
}

.camera-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 0.5em; /* Use em units to scale with font size */
  margin: 0.4em;
}

.camera-bottom {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 0.4em;
  margin-left: -0.4em;
  margin-right: -0.4em;
  margin-bottom: -0.4em;
  border-radius: 0 0 0.8em 0.8em;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.camera-name-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3em;
  width: 100%;
}

.camera-crown {
  font-size: 1.2em;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8));
  animation: crownGlow 2s ease-in-out infinite alternate;
}

@keyframes crownGlow {
  0% {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8)) drop-shadow(0 0 4px rgba(255, 215, 0, 0.3));
  }
  100% {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.8)) drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
  }
}

.camera-name {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  width: fit-content;
  margin-bottom: 0.5em;
  padding: 0.2em 0.5em;
  border-radius: 0.5em;
  background-color: #00000079;
}

.camera-rank {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.2em 0.5em;
  border-radius: 0.6em;
  backdrop-filter: blur(4px);
  white-space: nowrap;
  flex-shrink: 0;
  font-weight: bold;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  justify-content: center;
  display: flex;
  width: fit-content;
  margin-top: 0.5em;
  margin-left: 0.5em;
}

/* Rank color system */
.camera-rank.rank-1st {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
}

.camera-rank.rank-2nd {
  background: linear-gradient(45deg, #C0C0C0, #A8A8A8);
  box-shadow: 0 2px 8px rgba(192, 192, 192, 0.4);
}

.camera-rank.rank-3rd {
  background: linear-gradient(45deg, #CD7F32, #B8860B);
  box-shadow: 0 2px 8px rgba(205, 127, 50, 0.4);
}

.camera-rank.rank-other {
  background: linear-gradient(45deg, #4A90E2, #357ABD);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

.camera-points {
  font-weight: bold;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  padding: 0.2em 0.5em;
  border-radius: 0.6em;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
  white-space: nowrap;
  flex-shrink: 0;
  margin-top: 0.5em;
  margin-right: 0.5em;
}

.camera-no-stream {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
  border-radius: 12px;
}

.no-stream-message {
  color: #888;
  font-size: var(--dynamic-font-size, 12px);
  text-align: center;
  animation: pulse 2s infinite;
}

.empty-camera-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2a2a2a;
  color: #666;
  font-size: inherit; /* Inherit font size from container queries */
  border-radius: 12px;
}

/* Animation for the empty slot */
.empty-camera-placeholder span {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* Camera Display */
.camera-display {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  background: #000;
  border: 3px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.camera-display:hover {
  border-color: rgba(255, 255, 255, 0.3);
}

.camera-display.camera-on {
  box-shadow:
    0 0 20px rgba(76, 175, 80, 0.3),
    inset 0 0 20px rgba(76, 175, 80, 0.1);
}

.camera-display.camera-off {
  box-shadow:
    0 0 20px rgba(244, 67, 54, 0.3),
    inset 0 0 20px rgba(244, 67, 54, 0.1);
}

/* Camera Controls */
.camera-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 5;
}

.camera-status {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.recording-indicator {
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

.camera-status span {
  color: #ff4444;
  font-size: 10px;
  font-weight: 700;
  letter-spacing: 1px;
}

/* Camera Placeholder */
.camera-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  padding: 20px;
}

.camera-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.camera-icon.error {
  color: #ff4444;
}

.camera-icon svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.camera-status-text {
  font-size: 16px;
  font-weight: 600;
  margin: 8px 0 4px 0;
  color: rgba(255, 255, 255, 0.9);
}

.error-message {
  font-size: 14px;
  font-weight: 500;
  margin: 8px 0 4px 0;
  color: #ff6b6b;
}

.click-hint {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-style: italic;
}

/* Toggle Button Overlay */
.camera-toggle-overlay {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 10;
}

.toggle-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.toggle-button:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.toggle-button.on {
  border-color: rgba(76, 175, 80, 0.8);
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
}

.toggle-button.off {
  border-color: rgba(244, 67, 54, 0.8);
  box-shadow: 0 0 15px rgba(244, 67, 54, 0.3);
}

.toggle-button svg {
  width: 16px;
  height: 16px;
}

/* Responsive Design - Fallback for browsers without container query support */
@media (max-width: 768px) {
  .camera-container {
    padding: 8px;
  }

  .camera-overlay {
    padding: 0.2em;
  }

  /* Container queries will override these, but provide fallback */
  .camera-top {
    gap: 0.3em;
    margin: 0.3em;
  }

  .camera-bottom {
    padding: 0.3em;
    margin-left: -0.3em;
    margin-right: -0.3em;
    margin-bottom: -0.3em;
    border-radius: 0 0 0.5em 0.5em;
  }

  .camera-name {
    margin-bottom: 0.3em;
  }

  .camera-rank {
    padding: 0.15em 0.3em;
    border-radius: 0.4em;
  }

  .camera-points {
    padding: 0.15em 0.3em;
    border-radius: 0.4em;
  }

  .camera-toggle-overlay {
    bottom: 6px;
    right: 6px;
  }

  .toggle-button {
    width: 28px;
    height: 28px;
  }

  .toggle-button svg {
    width: 14px;
    height: 14px;
  }
}

/* Hover Effects */
.camera-display:hover .camera-icon {
  color: rgba(255, 255, 255, 0.9);
  transform: scale(1.1);
}

.camera-display:hover .click-hint {
  color: rgba(255, 255, 255, 0.8);
}

/* Animation for camera state changes */
.camera-display {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.camera-video {
  animation: fadeIn 0.5s ease-out;
}

.camera-placeholder {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Special Effects */
.camera-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  border-radius: 20px;
}

.camera-container:hover::before {
  opacity: 1;
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}