import React from 'react';
import HandGestureDetector from './HandGestureDetector';
import WordSelectionOverlay from './WordSelectionOverlay';
import './ScaledHandGestureDetector.css';

/**
 * ScaledHandGestureDetector - A wrapper component that scales HandGestureDetector to any size
 * 
 * Props:
 * - width: Target width in pixels (optional)
 * - height: Target height in pixels (optional)
 * - scale: Scale factor (e.g., 0.5 for half size, 2 for double size) (optional)
 * - maintainAspectRatio: Whether to maintain the original 4:3 aspect ratio (default: true)
 * - userData: User data to pass to HandGestureDetector
 * - onBack: Callback function to pass to HandGestureDetector
 * 
 * Usage examples:
 * <ScaledHandGestureDetector width={320} height={240} />
 * <ScaledHandGestureDetector scale={0.5} />
 * <ScaledHandGestureDetector width={800} maintainAspectRatio={true} />
 */
const ScaledHandGestureDetector = ({
  width,
  height,
  scale,
  maintainAspectRatio = true,
  userData,
  onBack,
  className = '',
  style = {},
  isDrawing, // Extract isDrawing to prevent it from being passed to DOM
  // Word selection props
  gameStatus = 'waiting', // 'waiting', 'wordSelection', 'playing', 'finished'
  wordOptions = [],
  wordSelectionTimeLeft = 0,
  onWordSelected,
  isCurrentDrawer = false,
  ...otherProps
}) => {
  // Original dimensions of HandGestureDetector (from the CSS)
  const ORIGINAL_WIDTH = 1280;
  const ORIGINAL_HEIGHT = 960;

  // Calculate the scaling parameters
  const getScalingParams = () => {
    let targetWidth, targetHeight, scaleX, scaleY;

    if (scale) {
      // If scale is provided, use it directly
      targetWidth = ORIGINAL_WIDTH * scale;
      targetHeight = ORIGINAL_HEIGHT * scale;
      scaleX = scaleY = scale;
    } else if (width && height) {
      // If both width and height are provided
      targetWidth = width;
      targetHeight = height;
      scaleX = width / ORIGINAL_WIDTH;
      scaleY = height / ORIGINAL_HEIGHT;
      
      if (maintainAspectRatio) {
        // Use the smaller scale to maintain aspect ratio
        const uniformScale = Math.min(scaleX, scaleY);
        scaleX = scaleY = uniformScale;
        targetWidth = ORIGINAL_WIDTH * uniformScale;
        targetHeight = ORIGINAL_HEIGHT * uniformScale;
      }
    } else if (width) {
      // If only width is provided
      scaleX = width / ORIGINAL_WIDTH;
      if (maintainAspectRatio) {
        scaleY = scaleX;
        targetHeight = ORIGINAL_HEIGHT * scaleX;
      } else {
        scaleY = 1;
        targetHeight = ORIGINAL_HEIGHT;
      }
      targetWidth = width;
    } else if (height) {
      // If only height is provided
      scaleY = height / ORIGINAL_HEIGHT;
      if (maintainAspectRatio) {
        scaleX = scaleY;
        targetWidth = ORIGINAL_WIDTH * scaleY;
      } else {
        scaleX = 1;
        targetWidth = ORIGINAL_WIDTH;
      }
      targetHeight = height;
    } else {
      // No scaling parameters provided, use original size
      targetWidth = ORIGINAL_WIDTH;
      targetHeight = ORIGINAL_HEIGHT;
      scaleX = scaleY = 1;
    }

    return {
      targetWidth,
      targetHeight,
      scaleX,
      scaleY,
      transform: `scale(${scaleX}, ${scaleY})`
    };
  };

  const scalingParams = getScalingParams();

  const containerStyle = {
    width: scalingParams.targetWidth,
    height: scalingParams.targetHeight,
    overflow: 'hidden',
    position: 'relative',
    ...style
  };

  const wrapperStyle = {
    transform: scalingParams.transform,
    transformOrigin: 'top left',
    width: ORIGINAL_WIDTH,
    height: ORIGINAL_HEIGHT,
  };

  // Determine if gesture detector should be disabled
  const isGestureDisabled = gameStatus === 'wordSelection' && isCurrentDrawer;

  return (
    <div
      className={`scaled-hand-gesture-detector ${className}`}
      style={containerStyle}
      {...otherProps}
    >
      <div
        className="scaled-hand-gesture-wrapper"
        style={wrapperStyle}
      >
        <HandGestureDetector
          userData={userData}
          onBack={onBack}
          scaledWidth={scalingParams.targetWidth}
          scaledHeight={scalingParams.targetHeight}
          scaleX={scalingParams.scaleX}
          scaleY={scalingParams.scaleY}
          disabled={isGestureDisabled}
        />

        {/* Word Selection Overlay - only show for current drawer during word selection */}
        {gameStatus === 'wordSelection' && isCurrentDrawer && (
          <WordSelectionOverlay
            wordOptions={wordOptions}
            timeLeft={wordSelectionTimeLeft}
            onWordSelected={onWordSelected}
            isVisible={true}
            disabled={false}
          />
        )}
      </div>
    </div>
  );
};

export default ScaledHandGestureDetector;
