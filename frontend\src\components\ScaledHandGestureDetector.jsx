import React, { useState, useEffect } from 'react';
import HandGestureDetector from './HandGestureDetector';
import './ScaledHandGestureDetector.css';

/**
 * ScaledHandGestureDetector - A wrapper component that scales HandGestureDetector to any size
 * 
 * Props:
 * - width: Target width in pixels (optional)
 * - height: Target height in pixels (optional)
 * - scale: Scale factor (e.g., 0.5 for half size, 2 for double size) (optional)
 * - maintainAspectRatio: Whether to maintain the original 4:3 aspect ratio (default: true)
 * - userData: User data to pass to HandGestureDetector
 * - onBack: Callback function to pass to HandGestureDetector
 * 
 * Usage examples:
 * <ScaledHandGestureDetector width={320} height={240} />
 * <ScaledHandGestureDetector scale={0.5} />
 * <ScaledHandGestureDetector width={800} maintainAspectRatio={true} />
 */
const ScaledHandGestureDetector = ({
  width,
  height,
  scale,
  maintainAspectRatio = true,
  userData,
  onBack,
  className = '',
  style = {},
  isDrawing, // Extract isDrawing to prevent it from being passed to DOM
  // Word selection props
  gameStatus = 'waiting', // 'waiting', 'wordSelection', 'playing', 'finished'
  wordOptions = [],
  wordSelectionTimeLeft = 0,
  onWordSelected,
  isCurrentDrawer = false,
  onDrawingData,
  ...otherProps
}) => {
  console.log('🔄 SCALED_COMPONENT: ScaledHandGestureDetector render with:', {
    gameStatus,
    isCurrentDrawer,
    wordOptionsCount: wordOptions.length,
    isWordSelectionMode: gameStatus === 'wordSelection' && isCurrentDrawer
  });

  // State to force re-render when dimensions change
  const [renderKey, setRenderKey] = useState(0);

  // Original dimensions of HandGestureDetector (from the CSS)
  const ORIGINAL_WIDTH = 1280;
  const ORIGINAL_HEIGHT = 960;

  // Calculate the scaling parameters
  const getScalingParams = () => {
    let targetWidth, targetHeight, scaleX, scaleY;

    if (scale) {
      // If scale is provided, use it directly
      targetWidth = ORIGINAL_WIDTH * scale;
      targetHeight = ORIGINAL_HEIGHT * scale;
      scaleX = scaleY = scale;
    } else if (width && height) {
      // If both width and height are provided
      targetWidth = width;
      targetHeight = height;
      scaleX = width / ORIGINAL_WIDTH;
      scaleY = height / ORIGINAL_HEIGHT;
      
      if (maintainAspectRatio) {
        // Use the smaller scale to maintain aspect ratio
        const uniformScale = Math.min(scaleX, scaleY);
        scaleX = scaleY = uniformScale;
        targetWidth = ORIGINAL_WIDTH * uniformScale;
        targetHeight = ORIGINAL_HEIGHT * uniformScale;
      }
    } else if (width) {
      // If only width is provided
      scaleX = width / ORIGINAL_WIDTH;
      if (maintainAspectRatio) {
        scaleY = scaleX;
        targetHeight = ORIGINAL_HEIGHT * scaleX;
      } else {
        scaleY = 1;
        targetHeight = ORIGINAL_HEIGHT;
      }
      targetWidth = width;
    } else if (height) {
      // If only height is provided
      scaleY = height / ORIGINAL_HEIGHT;
      if (maintainAspectRatio) {
        scaleX = scaleY;
        targetWidth = ORIGINAL_WIDTH * scaleY;
      } else {
        scaleX = 1;
        targetWidth = ORIGINAL_WIDTH;
      }
      targetHeight = height;
    } else {
      // No scaling parameters provided, use original size
      targetWidth = ORIGINAL_WIDTH;
      targetHeight = ORIGINAL_HEIGHT;
      scaleX = scaleY = 1;
    }

    return {
      targetWidth,
      targetHeight,
      scaleX,
      scaleY,
      transform: `scale(${scaleX}, ${scaleY})`
    };
  };

  // Force recalculation when width changes to ensure proper initial sizing
  useEffect(() => {
    // Small delay to ensure parent container has been sized
    const timer = setTimeout(() => {
      setRenderKey(prev => prev + 1);
    }, 100);

    return () => clearTimeout(timer);
  }, [width, height]);

  const scalingParams = getScalingParams();

  const containerStyle = {
    width: scalingParams.targetWidth,
    height: scalingParams.targetHeight,
    overflow: 'hidden',
    position: 'relative',
    ...style
  };

  const wrapperStyle = {
    transform: scalingParams.transform,
    transformOrigin: 'top left',
    width: ORIGINAL_WIDTH,
    height: ORIGINAL_HEIGHT,
  };

  // Determine if gesture detector should be disabled
  // Note: We should NOT disable during word selection because we need hand tracking for pinch detection
  // Only disable drawing/erasing gestures, not the entire hand tracking system
  const isGestureDisabled = false; // Keep hand tracking always enabled

  return (
    <div
      key={renderKey} // Force re-render when dimensions change
      className={`scaled-hand-gesture-detector ${className}`}
      style={containerStyle}
      {...otherProps}
    >
      <div
        className="scaled-hand-gesture-wrapper"
        style={wrapperStyle}
      >
        <HandGestureDetector
          userData={userData}
          onBack={onBack}
          scaledWidth={scalingParams.targetWidth}
          scaledHeight={scalingParams.targetHeight}
          scaleX={scalingParams.scaleX}
          scaleY={scalingParams.scaleY}
          disabled={isGestureDisabled}
          // Word selection props
          isWordSelectionMode={gameStatus === 'wordSelection' && isCurrentDrawer}
          wordOptions={wordOptions}
          onWordSelected={onWordSelected}
          wordSelectionTimeLeft={wordSelectionTimeLeft}
          // Drawing data callback
          onDrawingData={onDrawingData}
        />
      </div>
    </div>
  );
};

export default ScaledHandGestureDetector;
