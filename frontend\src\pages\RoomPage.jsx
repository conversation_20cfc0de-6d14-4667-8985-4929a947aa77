import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import Camera from '../components/Camera';
import ScaledHandGestureDetector from '../components/ScaledHandGestureDetector';
import DrawerViewComponent from '../components/DrawerViewComponent';
import './RoomPage.css';
import { useP2P } from '../contexts/P2PContext';
import GameManager from '../services/GameManager';
import leaveIcon from '../resources/leave.png';

const RoomPage = ({
  userData: initialUserData,
  roomData,
  isOwner: initialIsOwner = false,
  gameState: initialGameState = 'waiting', // 'waiting', 'playing', 'finished'
  onBack
}) => {
  const navigate = useNavigate();
  // Get P2P data from context
  const { p2pManager, userData: contextUserData, isOwner: contextIsOwner } = useP2P();

  // Debug P2P context and GameManager import
  // console.log('🔧 P2P Context Debug:', {
  //   hasP2pManager: !!p2pManager,
  //   p2pManagerType: typeof p2pManager,
  //   contextUserData: contextUserData?.name,
  //   contextIsOwner,
  //   initialUserData: initialUserData?.name,
  //   initialIsOwner
  // });

  // console.log('🔧 GameManager Import Debug:', {
  //   GameManagerType: typeof GameManager,
  //   GameManagerConstructor: GameManager?.name,
  //   canInstantiate: typeof GameManager === 'function'
  // });

  // Use context data if available, otherwise fall back to props
  const userData = contextUserData || initialUserData;
  const isOwner = contextIsOwner !== undefined ? contextIsOwner : initialIsOwner;



  const [gameState, setGameState] = useState(initialGameState);
  const [currentDrawer, setCurrentDrawer] = useState(null); // Current player drawing
  const [wordOptions, setWordOptions] = useState([]); // Available word choices for drawer
  const [wordSelectionTimeLeft, setWordSelectionTimeLeft] = useState(0); // Time left for word selection
  const [currentWord, setCurrentWord] = useState("AirDoodle"); // Currently selected word
  const [revealedLetters, setRevealedLetters] = useState([]); // Array of revealed letter indices
  const [timeLeft, setTimeLeft] = useState(0); // Time left for drawing
  const [drawingData, setDrawingData] = useState([]); // Drawing data from drawer
  const [drawerStream, setDrawerStream] = useState(null); // Drawer's video stream
  const [roomSettings, setRoomSettings] = useState({
    players: 10,
    language: 'English',
    drawTime: 80,
    rounds: 3,
    wordCount: 3,
    hints: 2,
    customWords: '',
    useCustomWordsOnly: false
  });

  const [chatMessages, setChatMessages] = useState([]);
  const [currentGuess, setCurrentGuess] = useState('');
  const chatMessagesRef = useRef(null);
  const [roomMembers, setRoomMembers] = useState([]);
  const [roomMembersVersion, setRoomMembersVersion] = useState(0); // Track changes for optimization
  const [ownershipVersion, setOwnershipVersion] = useState(0); // Track ownership changes for UI updates
  const [peerStreams, setPeerStreams] = useState(new Map());
  const [localStream, setLocalStream] = useState(null);
  const [gameManager, setGameManager] = useState(null);
  const [isRoomIdBlurred, setIsRoomIdBlurred] = useState(true);
  const [showCopyFeedback, setShowCopyFeedback] = useState(false);
  const [copyFeedbackText, setCopyFeedbackText] = useState('');
  const [gameCanvasWidth, setGameCanvasWidth] = useState(640);
  const gameCanvasRef = useRef(null);
  const mainContentRef = useRef(null);

  // Auto-scroll chat to bottom when new messages arrive
  useEffect(() => {
    if (chatMessagesRef.current) {
      chatMessagesRef.current.scrollTop = chatMessagesRef.current.scrollHeight;
    }
  }, [chatMessages]);

  useEffect(() => {
    const node = gameCanvasRef.current;
    if (!node) return;

    const handleSizeChange = () => {
      const width = node.clientWidth;
      const height = node.clientHeight;
      //console.log('Game canvas width:', width);
      //console.log('Game canvas height:', height);
      const possibleHeight = Math.floor(width * 0.75);
      const possibleWidth = Math.floor(height * 1.33);
      if (possibleHeight < height) {
        if (possibleWidth < width) {
          setGameCanvasWidth(width);
        } else {
          setGameCanvasWidth(possibleWidth);
        }
      } else {
        setGameCanvasWidth(possibleWidth);
      }
    };

    // Initial size check with a small delay to ensure DOM is ready
    const initialSizeCheck = () => {
      handleSizeChange();
      // Double-check after a short delay to ensure proper sizing
      setTimeout(handleSizeChange, 50);
    };

    initialSizeCheck();

    // Create ResizeObserver to monitor size changes
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        // console.log('Game canvas size changed:', {
        //   width: entry.contentRect.width,
        //   height: entry.contentRect.height
        // });
        handleSizeChange();
      }
    });

    // Start observing the node
    resizeObserver.observe(node);

    // Cleanup function
    return () => {
      resizeObserver.disconnect();
    };
  }, [gameCanvasRef.current?.clientWidth, gameCanvasRef.current?.clientHeight]);

  // Additional effect to trigger resize when game state changes
  useEffect(() => {
    const node = gameCanvasRef.current;
    if (!node) return;

    // Trigger resize calculation when game state changes
    const timer = setTimeout(() => {
      const width = node.clientWidth;
      const height = node.clientHeight;
      const possibleHeight = Math.floor(width * 0.75);
      const possibleWidth = Math.floor(height * 1.33);
      if (possibleHeight < height) {
        if (possibleWidth < width) {
          setGameCanvasWidth(width);
        } else {
          setGameCanvasWidth(possibleWidth);
        }
      } else {
        setGameCanvasWidth(possibleWidth);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [gameState]);

  // Initialize GameManager when P2P manager is ready
  useEffect(() => {
    // console.log('🔧 GameManager useEffect triggered:', {
    //   hasP2pManager: !!p2pManager,
    //   hasGameManager: !!gameManager,
    //   isOwner: contextIsOwner
    // });

    if (!p2pManager || gameManager) {
      // console.log('🔧 Skipping GameManager creation:', {
      //   reason: !p2pManager ? 'No P2P manager' : 'GameManager already exists'
      // });
      return; // Don't create if already exists
    }

    if (p2pManager && typeof p2pManager.on === 'function') {
      try {
        // console.log('✅ Creating GameManager with P2P Manager');
        const manager = new GameManager(p2pManager);

        // Link GameManager to P2PManager
        p2pManager.setGameManager(manager);

        // console.log('✅ GameManager created and linked successfully');
        setGameManager(manager);
      } catch (error) {
        console.error('❌ Error creating GameManager:', error);
        console.error('❌ Error stack:', error.stack);
      }
    } else {
      // console.log('❌ P2P manager not ready for GameManager creation');
    }
  }, [p2pManager, gameManager]);

  // Setup P2P event listeners
  useEffect(() => {
    if (!p2pManager) return;

    const handleRoomStateUpdate = (roomState) => {
      //console.log('Room state updated:', roomState);
      //console.log('Current peer streams:', Array.from(peerStreams.keys()));
      //console.log('Room members:', roomState.players?.map(p => ({ name: p.name, peerId: p.peerId })));

      const newPlayers = roomState.players || [];

      // Check if room members actually changed (optimization)
      const currentMemberIds = roomMembers.map(m => m.peerId).sort();
      const newMemberIds = newPlayers.map(m => m.peerId).sort();
      const membersChanged = JSON.stringify(currentMemberIds) !== JSON.stringify(newMemberIds);

      if (membersChanged) {
        //console.log('Room members changed, updating camera slots');
        setRoomMembersVersion(prev => prev + 1);
      }

      setRoomMembers(newPlayers);
      setRoomSettings(roomState.settings || roomSettings);
    };

    const handleStreamReceived = ({ peerId, stream }) => {
      /*console.log('🎥 Received stream from peer:', peerId, 'Stream details:', {
        id: stream.id,
        active: stream.active,
        tracks: stream.getTracks().length,
        videoTracks: stream.getVideoTracks().length,
        audioTracks: stream.getAudioTracks().length
      });*/

      // Check if stream has video tracks
      const videoTracks = stream.getVideoTracks();
      if (videoTracks.length === 0) {
        console.warn('⚠️ Received stream has no video tracks:', peerId);
      } else {
        /*console.log('✅ Stream has video tracks:', videoTracks.map(t => ({
          id: t.id,
          enabled: t.enabled,
          readyState: t.readyState
        })));*/
      }

      // Don't add the stream if it's from the local user
      if (peerId !== userData?.peerId) {
        setPeerStreams(prev => {
          const newStreams = new Map(prev);
          newStreams.set(peerId, stream);
          //console.log('📡 Updated peer streams:', Array.from(newStreams.keys()));
          //console.log('📡 Stream map size:', newStreams.size);
          return newStreams;
        });

        // Set drawer stream if it's from the current drawer
        if (currentDrawer && peerId === currentDrawer.peerId) {
          // console.log('📹 Setting drawer stream from:', peerId);
          setDrawerStream(stream);
        }

        // Trigger camera slots re-render when new stream is received
        setRoomMembersVersion(prev => prev + 1);
      } else {
        //console.log('🚫 Ignoring stream from local user:', peerId);
      }
    };

    const handlePeerDisconnected = (peerId) => {
      //console.log('Peer disconnected:', peerId);
      setPeerStreams(prev => {
        const newStreams = new Map(prev);
        newStreams.delete(peerId);
        return newStreams;
      });
    };

    const handleOwnershipChanged = ({ isOwner: newIsOwner, newOwner }) => {
      // console.log('👑 RoomPage: Ownership changed:', { newIsOwner, newOwner });
      // Force re-render of camera slots to update crown display
      setOwnershipVersion(prev => prev + 1);
    };

    const handleSettingsChanged = (newSettings) => {
      // console.log('⚙️ Settings changed from P2P:', newSettings);
      setRoomSettings(newSettings);
    };

    const handleGameStateUpdated = (gameState) => {
      // console.log('🎮 Game state updated from P2P:', gameState);

      // Update local game state for non-owner players
      if (!isOwner && gameState) {
        if (gameState.status === 'wordSelection') {
          setGameState('wordSelection');
          setCurrentDrawer(gameState.currentDrawer);
          setWordOptions(gameState.currentWordOptions || []);
          setWordSelectionTimeLeft(gameState.wordSelectionTimeLeft || 0);
        } else if (gameState.status === 'playing') {
          setGameState('playing');
          setCurrentDrawer(gameState.currentDrawer);
          setCurrentWord(gameState.currentWord);
          setTimeLeft(gameState.timeLeft || 0);
        } else if (gameState.status === 'finished') {
          setGameState('finished');
        } else if (gameState.status === 'waiting') {
          setGameState('waiting');
          setCurrentDrawer(null);
          setCurrentWord(null);
          setTimeLeft(0);
        }
      }
    };

    const handleDrawingData = ({ peerId, data }) => {
      // console.log('🎨 Received drawing data from:', peerId);
      // Only update drawing data if it's from the current drawer
      if (currentDrawer && peerId === currentDrawer.peerId) {
        // Handle both old format (array) and new format (object with normalized points)
        setDrawingData(data);
      }
    };



    p2pManager.on('roomStateUpdated', handleRoomStateUpdate);
    p2pManager.on('streamReceived', handleStreamReceived);
    p2pManager.on('peerDisconnected', handlePeerDisconnected);
    p2pManager.on('ownershipChanged', handleOwnershipChanged);
    p2pManager.on('settingsChanged', handleSettingsChanged);
    p2pManager.on('gameStateUpdated', handleGameStateUpdated);
    p2pManager.on('drawingData', handleDrawingData);

    // Start local camera
    const startCamera = async () => {
      try {
        const stream = await p2pManager.startCamera();
        setLocalStream(stream);
      } catch (error) {
        console.error('Failed to start camera:', error);
      }
    };
    startCamera();

    // Initial room state
    const initialRoomState = p2pManager.getRoomState();
    if (initialRoomState) {
      handleRoomStateUpdate(initialRoomState);
    }

    return () => {
      p2pManager.off('roomStateUpdated', handleRoomStateUpdate);
      p2pManager.off('streamReceived', handleStreamReceived);
      p2pManager.off('peerDisconnected', handlePeerDisconnected);
      p2pManager.off('ownershipChanged', handleOwnershipChanged);
      p2pManager.off('settingsChanged', handleSettingsChanged);
      p2pManager.off('gameStateUpdated', handleGameStateUpdated);
      p2pManager.off('drawingData', handleDrawingData);
      p2pManager.stopCamera();
    };
  }, [p2pManager, isOwner, currentDrawer]);

  // Setup GameManager event listeners
  useEffect(() => {
    if (!gameManager) return;

    const handleWordSelectionStarted = ({ round, drawer, wordOptions: options, wordSelectionTimeLeft: selectionTimeLeft }) => {
      // console.log('🎮 Word selection started:', { round, drawer: drawer?.name, wordOptions: options, timeLeft: selectionTimeLeft });
      setGameState('wordSelection');
      setCurrentDrawer(drawer);
      setWordOptions(options);
      setWordSelectionTimeLeft(selectionTimeLeft);
      setTimeLeft(0); // No drawing timer during word selection
    };

    const handleDrawingPhaseStarted = ({ round, drawer, word, timeLeft: roundTimeLeft }) => {
      // console.log('🎮 Drawing phase started:', { round, drawer: drawer?.name, word, timeLeft: roundTimeLeft });
      setGameState('playing');
      setCurrentDrawer(drawer);
      setTimeLeft(roundTimeLeft); // Set drawing timer
      setWordSelectionTimeLeft(0); // Clear word selection timer
    };

    const handleGameEnded = ({ finalScores, winner }) => {
      // console.log('🏁 Game ended:', { winner: winner?.player?.name, finalScores });
      setGameState('finished');
      setTimeLeft(0); // Reset timer
    };

    const handleTimerUpdate = (newTimeLeft) => {
      // console.log('⏰ Timer update:', newTimeLeft);
      setTimeLeft(newTimeLeft); // Update timer display
    };

    const handleWordSelectionTimerUpdate = (newTimeLeft) => {
      // console.log('⏰ Word selection timer update:', newTimeLeft);
      setWordSelectionTimeLeft(newTimeLeft); // Update word selection timer display
    };

    const handleGameReset = () => {
      // console.log('🔄 Game reset');
      setGameState('waiting');
      setCurrentDrawer(null);
      setTimeLeft(0);
    };

    const handleWordSelected = ({ word, drawer }) => {
      // console.log('📝 Word selected:', word, 'by:', drawer?.name);
      // Could show word to drawer or update UI
    };

    const handleCorrectGuess = ({ player, points, timeBonus }) => {
      console.log('✅ Correct guess by:', player?.name, 'Points:', points);
      // Add system message to chat
      setChatMessages(prev => [...prev, {
        id: Date.now(),
        type: 'correct',
        author: 'System',
        text: `${player?.name} guessed correctly! +${points} points`
      }]);
    };

    const handleNewGuess = (guessData) => {
      // console.log('💭 New guess:', guessData);
      // Add guess to chat if it's from another player
      if (guessData.peerId !== userData?.peerId) {
        setChatMessages(prev => [...prev, {
          id: Date.now(),
          type: guessData.isCorrect ? 'correct' : 'guess',
          author: guessData.playerName,
          text: guessData.guess
        }]);
      }
    };

    gameManager.on('wordSelectionStarted', handleWordSelectionStarted);
    gameManager.on('drawingPhaseStarted', handleDrawingPhaseStarted);
    gameManager.on('gameEnded', handleGameEnded);
    gameManager.on('timerUpdate', handleTimerUpdate);
    gameManager.on('wordSelectionTimerUpdate', handleWordSelectionTimerUpdate);
    gameManager.on('gameReset', handleGameReset);
    gameManager.on('wordSelected', handleWordSelected);
    gameManager.on('correctGuess', handleCorrectGuess);
    gameManager.on('newGuess', handleNewGuess);

    return () => {
      gameManager.off('wordSelectionStarted', handleWordSelectionStarted);
      gameManager.off('drawingPhaseStarted', handleDrawingPhaseStarted);
      gameManager.off('gameEnded', handleGameEnded);
      gameManager.off('timerUpdate', handleTimerUpdate);
      gameManager.off('wordSelectionTimerUpdate', handleWordSelectionTimerUpdate);
      gameManager.off('gameReset', handleGameReset);
      gameManager.off('wordSelected', handleWordSelected);
      gameManager.off('correctGuess', handleCorrectGuess);
      gameManager.off('newGuess', handleNewGuess);
    };
  }, [gameManager, userData]);

  // Check for valid username on mount and when userData changes
  useEffect(() => {
    const checkUserAccess = () => {
      // If no userData or no name, redirect to start page
      if (!userData?.name || userData.name.trim() === '') {
        //console.log('No valid username found, redirecting to start page');
        navigate('/', { 
          state: { 
            message: 'Please enter your name before joining a room',
            roomId: p2pManager?.roomId || roomData?.id 
          }
        });
        return false;
      }
      return true;
    };

    // Perform initial check
    if (!checkUserAccess()) {
      return; // Stop further execution if check fails
    }

    // Additional check: if we have a room ID but no P2P connection
    if ((p2pManager?.roomId || roomData?.id) && !p2pManager?.socket?.connected) {
      //console.log('No valid connection found, redirecting to start page');
      navigate('/', { 
        state: { 
          message: 'Please join the room properly through the start page',
          roomId: p2pManager?.roomId || roomData?.id 
        }
      });
    }
  }, [userData, navigate, p2pManager, roomData]);

  const handleSettingChange = (setting, value) => {
    //console.log('Setting change requested:', { setting, value, isOwner, hasP2P: !!p2pManager });
    if (!isOwner || !p2pManager) return; // Only owner can change settings

    const newSettings = {
      ...roomSettings,
      [setting]: value
    };

    //console.log('Updating settings locally and broadcasting:', newSettings);
    setRoomSettings(newSettings);
    p2pManager.updateSettings(newSettings);
  };

  const handleStartGame = () => {
    // console.log('🎮 Start game button clicked', {
    //   isOwner,
    //   hasGameManager: !!gameManager,
    //   gameState,
    //   isActiveManager: gameManager?.isActiveManager(),
    //   roomSettings
    // });

    if (!isOwner || !gameManager) {
      // console.warn('❌ Cannot start game:', { isOwner, hasGameManager: !!gameManager });
      return;
    }

    if (gameState === 'waiting') {
      // console.log('🎮 Starting game with settings:', roomSettings);
      try {
        // Only active GameManager can start games
        if (gameManager.isActiveManager()) {
          gameManager.startGame(roomSettings);
          // console.log('✅ GameManager.startGame() called successfully');
        } else {
          console.warn('❌ GameManager not active, cannot start game');
        }
      } catch (error) {
        console.error('❌ Error starting game:', error);
      }
    } else {
      // Reset game to waiting state
      // console.log('🔄 Resetting game to waiting state');
      if (gameManager.isActiveManager()) {
        gameManager.resetGame();
      }
      setGameState('waiting');
    }
  };

  const handleCopyRoomId = async () => {
    const roomId = p2pManager?.roomId || roomData?.id;
    if (roomId) {
      try {
        await navigator.clipboard.writeText(roomId);
        setCopyFeedbackText('Room ID copied!');
        setShowCopyFeedback(true);
        setTimeout(() => setShowCopyFeedback(false), 2000);
      } catch (error) {
        console.error('Failed to copy room ID:', error);
        setCopyFeedbackText('Failed to copy');
        setShowCopyFeedback(true);
        setTimeout(() => setShowCopyFeedback(false), 2000);
      }
    }
  };

  const toggleRoomIdBlur = () => {
    setIsRoomIdBlurred(!isRoomIdBlurred);
  };

  const getRank = (peerId) => {
    // Check if we have valid scores data
    const scores = p2pManager?.roomState?.scores;
    if (!scores || !roomMembers.length) {
      return peerId === userData?.peerId ? '1st' : 'N/A';
    }

    // Ensure scores is a Map, if not convert it or use empty Map
    const scoresMap = scores instanceof Map ? scores : new Map();

    // Sort players by score and find the index of the given peerId
    const sortedPlayers = [...roomMembers].sort((a, b) => {
      const scoreA = scoresMap.get(a.peerId) || 0;
      const scoreB = scoresMap.get(b.peerId) || 0;
      return scoreB - scoreA;
    });

    const index = sortedPlayers.findIndex(p => p.peerId === peerId);
    if (index === -1) {
      // If player not found in sorted list but it's the local user
      if (peerId === userData?.peerId) {
        return '1st'; // Local user is first when they just created the room
      }
      return 'N/A';
    }

    const rank = index + 1;
    const suffix = ['st', 'nd', 'rd'][rank - 1] || 'th';
    return `${rank}${suffix}`;
  };

  const getScore = (peerId) => {
    const scores = p2pManager?.roomState?.scores;
    if (!scores) {
      return 0;
    }

    // Ensure scores is a Map, if not return 0
    const scoresMap = scores instanceof Map ? scores : new Map();
    return scoresMap.get(peerId) || 0;
  };

  // Helper function to determine if a member is the room owner
  const isMemberOwner = useCallback((member) => {
    if (!p2pManager || !member) return false;

    // Get all players sorted by join time (oldest first)
    const roomState = p2pManager.getRoomState();
    if (!roomState || !roomState.players) return false;

    const sortedPlayers = roomState.players.sort((a, b) => a.joinTime - b.joinTime);
    const isOwner = sortedPlayers.length > 0 && sortedPlayers[0].peerId === member.peerId;



    return isOwner;
  }, [p2pManager, roomMembers, ownershipVersion]); // Add ownershipVersion to trigger re-renders on ownership changes

  // Format timer display (seconds to MM:SS)
  const formatTimer = (seconds) => {
    if (seconds <= 0) return '--:--';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Get timer CSS class based on time left
  const getTimerClass = (seconds) => {
    if (seconds <= 10) return 'timer-value critical';
    if (seconds <= 30) return 'timer-value warning';
    return 'timer-value';
  };



  const handleWordSelection = (selectedWord) => {
    // console.log('🎮 Word selected by user:', selectedWord);

    // Set the current word and start the game
    setCurrentWord(selectedWord);
    setGameState('playing');
    setRevealedLetters([]);
    setTimeLeft(roomSettings.drawTime);

    // Start the drawing timer
    startDrawingTimer();

    // Start the hint reveal system
    startHintRevealSystem(selectedWord);

    // Send word selection to GameManager via P2PManager
    if (p2pManager && userData?.peerId) {
      p2pManager.sendGameAction('selectWord', {
        word: selectedWord
      });
    }
  };

  const handleDrawingDataUpdate = (drawingData) => {
    // drawingData now contains: { points: [...], canvasWidth, canvasHeight, timestamp }
    const pointCount = drawingData?.points?.length || drawingData?.length || 0;
    // console.log('🎨 Drawing data updated:', pointCount, 'points');

    // Send drawing data to other players via P2P
    if (p2pManager && currentDrawer?.peerId === userData?.peerId) {
      p2pManager.sendDrawingData(drawingData);
    }
  };

  // Function to start the drawing timer
  const startDrawingTimer = () => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          // Time's up - end the round
          setGameState('finished');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Function to start the hint reveal system
  const startHintRevealSystem = (word) => {
    const totalHints = roomSettings.hints;
    const drawTime = roomSettings.drawTime;

    if (totalHints === 0) return; // No hints to reveal

    // Calculate when to reveal each hint (at 80% of time divided by number of hints)
    const timeForHints = drawTime * 0.8; // 80% of total time
    const revealInterval = timeForHints / totalHints;

    for (let i = 0; i < totalHints; i++) {
      setTimeout(() => {
        revealRandomLetter(word);
      }, revealInterval * (i + 1) * 1000); // Convert to milliseconds
    }
  };

  // Function to reveal a random letter
  const revealRandomLetter = (word) => {
    setRevealedLetters(prev => {
      // Get letters that haven't been revealed yet (excluding spaces)
      const unrevealedIndices = [];
      for (let i = 0; i < word.length; i++) {
        if (word[i] !== ' ' && !prev.includes(i)) {
          unrevealedIndices.push(i);
        }
      }

      if (unrevealedIndices.length === 0) return prev; // All letters revealed

      // Pick a random unrevealed letter
      const randomIndex = unrevealedIndices[Math.floor(Math.random() * unrevealedIndices.length)];
      return [...prev, randomIndex];
    });
  };

  // Function to create masked word display
  const createMaskedWord = (word, revealedIndices) => {
    if (!word) return '';

    return word.split('').map((char, index) => {
      if (char === ' ') return ' '; // Keep spaces
      if (revealedIndices.includes(index)) return char; // Show revealed letters
      return '_'; // Hide unrevealed letters
    }).join('');
  };

  const handleChatSubmit = (e) => {
    e.preventDefault();
    if (!currentGuess.trim() || gameState === 'waiting' || gameState === 'wordSelection') return;

    // Add user's guess to chat
    const newMessage = {
      id: Date.now(),
      type: 'guess',
      author: userData?.name || 'You',
      text: currentGuess.trim()
    };

    setChatMessages(prev => [...prev, newMessage]);

    // Send guess to GameManager via P2PManager
    if (p2pManager && userData?.peerId) {
      p2pManager.sendGameAction('submitGuess', {
        guess: currentGuess.trim()
      });
    }

    setCurrentGuess('');
  };

  // Memoized camera slots - only re-render when members or streams change
  const cameraSlots = useMemo(() => {
    const slots = [];
    const maxSlots = roomSettings.players; 

    /*console.log('Rendering camera slots (memoized):', {
      roomMembers: roomMembers.length,
      peerStreams: peerStreams.size,
      localStream: !!localStream,
      userData: userData?.name,
      userPeerId: userData?.peerId,
      version: roomMembersVersion
    });*/

    // First slot is always the user's camera (local stream)
    slots.push(
      <div key="user-camera" className="camera-slot user-camera">
        <Camera
          name={userData?.name || 'You'}
          rank={getRank(userData?.peerId)}
          points={getScore(userData?.peerId)}
          stream={localStream}
          isLocal={true}
          isOwner={isOwner}
        />
      </div>
    );

    // Get all room members except the local user
    // Filter by peerId and also by the P2P manager's local user data to be extra safe
    const localPeerId = userData?.peerId || p2pManager?.localUserData?.peerId;
    const localUserId = userData?.id || p2pManager?.localUserData?.id;

    const otherMembers = roomMembers.filter(member => {
      // Exclude if peerId matches
      if (member.peerId === localPeerId) return false;
      // Exclude if id matches (backup check)
      if (member.id === localUserId) return false;
      // Exclude if this is the exact same user object
      if (member === userData) return false;
      return true;
    });

    //console.log('Other members (filtered):', otherMembers.map(m => ({ name: m.name, peerId: m.peerId, id: m.id })));
    //console.log('Local user peerId:', localPeerId);
    //console.log('Local user id:', localUserId);

    // Fill remaining slots with peer cameras or empty slots
    for (let i = 1; i < maxSlots; i++) {
      const memberIndex = i - 1;
      const member = otherMembers[memberIndex];

      if (member) {
        // Double-check: never render the local user as a peer
        const isLocalUser = member.peerId === localPeerId ||
                           member.id === localUserId ||
                           member === userData;

        if (isLocalUser) {
          console.warn(`Prevented local user from appearing in peer slot ${i}:`, member);
          // Render empty slot instead
          slots.push(
            <div key={`empty-${i}`} className="camera-slot">
              <div className="empty-camera-placeholder">
                <span>Waiting for player...</span>
              </div>
            </div>
          );
          continue;
        }

        const stream = peerStreams.get(member.peerId);
        /*console.log(`📹 Slot ${i}: Member ${member.name} (${member.peerId})`, {
          hasStream: !!stream,
          streamId: stream?.id,
          streamActive: stream?.active,
          videoTracks: stream?.getVideoTracks()?.length || 0
        });*/

        slots.push(
          <div key={member.peerId} className="camera-slot">
            <Camera
              name={member.name || 'Peer'}
              rank={getRank(member.peerId)}
              points={getScore(member.peerId)}
              stream={stream}
              isLocal={false}
              isOwner={isMemberOwner(member)}
            />
          </div>
        );
      } else {
        slots.push(
          <div key={`empty-${i}`} className="camera-slot">
            <div className="empty-camera-placeholder">
              <span>Waiting for player...</span>
            </div>
          </div>
        );
      }
    }

    return slots;
  }, [roomMembers, roomMembersVersion, ownershipVersion, peerStreams, localStream, userData, p2pManager, isOwner, isMemberOwner]);

  return (
    <div className="room-page" style={{ overflowY: 'auto', maxHeight: '100vh' }}>
      {/* Header */}
      <div className="room-header">
        <div className="room-title">
          <h1>AirDoodle</h1>
          <div className="room-info">
            <span className="round-info">Round 1 of {roomSettings.rounds}</span>
            <span className={`game-status ${gameState}`}>
              {gameState === 'waiting' ? 'WAITING' :
               gameState === 'wordSelection' ? 'WORD SELECTION' :
               gameState === 'playing' ? 'PLAYING' : 'FINISHED'}
            </span>
          </div>
        </div>
        {/* Room ID display */}
        <div className="room-id-display">
          <div 
            className={`room-id-text ${isRoomIdBlurred ? 'blurred' : ''}`}
            onClick={toggleRoomIdBlur}
            data-room-id={p2pManager?.roomId || roomData?.id || 'Unknown'}
          >
            Room ID: {p2pManager?.roomId || roomData?.id || 'Unknown'}
          </div>
          <button className="copy-button" onClick={handleCopyRoomId}>
            Copy ID
          </button>
          <div className={`copy-feedback ${showCopyFeedback ? 'visible' : ''}`}>
            {copyFeedbackText}
          </div>
        </div>
        <div className="header-buttons">
          {isOwner && gameState !== 'waiting' && (
            <button
              className="end-game-btn"
              onClick={handleStartGame}
              title="Reset Game"
            >
              ⏪
            </button>
          )}

          <button
            className="leave-room-btn"
            onClick={() => window.location.href = '/'}
            title="Leave Room"
          >
            <img src={leaveIcon} alt="Leave Room" width="25" height="25" style={{ marginLeft: '3px' }} />
          </button>
        </div>
      </div>

      <div className="room-content">
        {/* Left Camera Grid - 2x5 layout */}
        <div className="camera-grid">
          {cameraSlots}
        </div>

        {/* Main Content Area - Centered */}
        <div className="main-content" ref={mainContentRef}>
          {/* Top horizontal bar above the game canvas - show during word selection and gameplay */}
          {(gameState === 'wordSelection' || gameState === 'playing') && (
            <div className="game-top-bar">
              {/* Timer on the left */}
              <div className="game-timer">
                <span className="timer-icon">⏱️</span>
                <span className={getTimerClass(gameState === 'wordSelection' ? wordSelectionTimeLeft : timeLeft)}>
                  {formatTimer(gameState === 'wordSelection' ? wordSelectionTimeLeft : timeLeft)}
                </span>
              </div>

              {/* Word in the center */}
              <div className="game-word-display">
                {currentWord ? (
                  currentDrawer?.peerId === userData?.peerId ? (
                    // Show full word to the drawer
                    <span className="drawer-word">{currentWord}</span>
                  ) : (
                    // Show masked word with hints to other players
                    <span className="guessing-word">{createMaskedWord(currentWord, revealedLetters)}</span>
                  )
                ) : (
                  'Drawing in progress'
                )}
              </div>

              {/* Right side - reserved for future elements */}
              <div className="game-top-bar-right"></div>
            </div>
          )}



            {gameState === 'waiting' ? (
              /* Game Settings */
              <div className="game-settings">
                <div className="settings-grid">
                  <div className="setting-group">
                    <label>👥 Players</label>
                    <select
                      value={roomSettings.players}
                      onChange={(e) => handleSettingChange('players', parseInt(e.target.value))}
                      disabled={!isOwner}
                    >
                      {[2,3,4,5,6,7,8,9,10].map(num => (
                        <option key={num} value={num}>{num}</option>
                      ))}
                    </select>
                  </div>

                  <div className="setting-group">
                    <label>🌐 Language</label>
                    <select
                      value={roomSettings.language}
                      onChange={(e) => handleSettingChange('language', e.target.value)}
                      disabled={!isOwner}
                    >
                      <option value="English">English</option>
                      <option value="Spanish">Spanish</option>
                      <option value="French">French</option>
                      <option value="German">German</option>
                    </select>
                  </div>

                  <div className="setting-group">
                    <label>⏱️ Draw time</label>
                    <select
                      value={roomSettings.drawTime}
                      onChange={(e) => handleSettingChange('drawTime', parseInt(e.target.value))}
                      disabled={!isOwner}
                    >
                      {[30,45,60,75,80,90,105,120,150,180].map(time => (
                        <option key={time} value={time}>{time}</option>
                      ))}
                    </select>
                  </div>

                  <div className="setting-group">
                    <label>🔄 Rounds</label>
                    <select
                      value={roomSettings.rounds}
                      onChange={(e) => handleSettingChange('rounds', parseInt(e.target.value))}
                      disabled={!isOwner}
                    >
                      {[1,2,3,4,5,6,7,8,9,10].map(num => (
                        <option key={num} value={num}>{num}</option>
                      ))}
                    </select>
                  </div>

                  <div className="setting-group">
                    <label>📝 Word Count</label>
                    <select
                      value={roomSettings.wordCount}
                      onChange={(e) => handleSettingChange('wordCount', parseInt(e.target.value))}
                      disabled={!isOwner}
                    >
                      {[1,2,3,4,5].map(num => (
                        <option key={num} value={num}>{num}</option>
                      ))}
                    </select>
                  </div>

                  <div className="setting-group">
                    <label>💡 Hints</label>
                    <select
                      value={roomSettings.hints}
                      onChange={(e) => handleSettingChange('hints', parseInt(e.target.value))}
                      disabled={!isOwner}
                    >
                      {[0,1,2,3,4,5].map(num => (
                        <option key={num} value={num}>{num}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="custom-words-section">
                  <div className="custom-words-header">
                    <label>📝 Custom words</label>
                    <label className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={roomSettings.useCustomWordsOnly}
                        onChange={(e) => handleSettingChange('useCustomWordsOnly', e.target.checked)}
                        disabled={!isOwner}
                      />
                      Use custom words only
                    </label>
                  </div>
                  <textarea
                    placeholder="Minimum of 10 words, 1-32 characters per word, 20000 characters maximum. Separated by a , (comma)"
                    value={roomSettings.customWords}
                    onChange={(e) => handleSettingChange('customWords', e.target.value)}
                    disabled={!isOwner}
                    className="custom-words-input"
                  />
                </div>
                <button
                    className="start-btn"
                    onClick={handleStartGame}
                    disabled={!isOwner}
                  >
                    {gameState === 'waiting' ? 'Start Game!' : 'Stop Game'}
                  </button>

                  {/* Temporary test button for word selection */}
                  <button
                    className="start-btn"
                    onClick={() => {
                      setGameState('wordSelection');
                      setCurrentDrawer(userData);
                      setWordOptions(['Taco', 'Happy face', 'Baseball']);
                      setWordSelectionTimeLeft(20);
                      setCurrentWord(null);
                      setRevealedLetters([]);
                    }}
                    style={{ backgroundColor: '#ff6b6b', marginLeft: '10px' }}
                  >
                    Test Word Selection
                  </button>
              </div>
          ) : gameState === 'wordSelection' && currentDrawer?.peerId !== userData?.peerId ? (
            /* Word Selection - Non-drawer view */
            <div className="game-canvas" ref={gameCanvasRef}>
              <div className="word-selection-waiting">
                <div className="waiting-message">
                  <div className="waiting-icon">🎨</div>
                  <div className="waiting-text">
                    <strong>{currentDrawer?.name || 'Player'}</strong> is selecting a word...
                  </div>
                  <div className="waiting-timer">
                    {wordSelectionTimeLeft > 0 && `${wordSelectionTimeLeft}s remaining`}
                  </div>
                </div>
              </div>
            </div>
          ) : gameState === 'playing' && currentDrawer?.peerId !== userData?.peerId ? (
            /* Playing Phase - Non-drawer view (watching drawer) */
            <div className="game-canvas" ref={gameCanvasRef}>
              <DrawerViewComponent
                drawerStream={drawerStream}
                drawingData={drawingData}
                currentWord={createMaskedWord(currentWord, revealedLetters)}
                drawerName={currentDrawer?.name || 'Player'}
                width={gameCanvasWidth}
                height={gameCanvasWidth * 0.75}
              />
            </div>
          ) : (
            /* Game Canvas - Hand Gesture Detector (for drawer or other phases) */
            <div className="game-canvas" ref={gameCanvasRef}>
              <ScaledHandGestureDetector
                userData={currentDrawer}
                isDrawing={currentDrawer?.peerId === userData?.peerId}
                onBack={() => {}}
                width={gameCanvasWidth}
                gameStatus={gameState}
                wordOptions={wordOptions}
                wordSelectionTimeLeft={wordSelectionTimeLeft}
                onWordSelected={handleWordSelection}
                isCurrentDrawer={currentDrawer?.peerId === userData?.peerId}
                onDrawingData={handleDrawingDataUpdate}
              />
            </div>
          )}
        </div>

        {/* Chat Sidebar */}
        <div className="chat-sidebar">
          <div className="chat-header">
            💬 Chat
          </div>

          <div className="chat-messages" ref={chatMessagesRef}>
            {chatMessages.map((message) => (
              <div key={message.id} className={`chat-message ${message.type}`}>
                {message.author && (
                  <span className="message-author">{message.author}:</span>
                )}
                <span className="message-text">{message.text}</span>
              </div>
            ))}
          </div>

          <div className="chat-input-container">
            <form onSubmit={handleChatSubmit}>
              <input
                type="text"
                className="chat-input-field"
                placeholder={
                  gameState === 'waiting' ? "Chat will be enabled when game starts..." :
                  gameState === 'wordSelection' ? "Word selection in progress..." :
                  "Type your guess here..."
                }
                value={currentGuess}
                onChange={(e) => setCurrentGuess(e.target.value)}
                disabled={gameState === 'waiting' || gameState === 'wordSelection'}
                maxLength={100}
              />
            </form>
          </div>
        </div>
      </div>


    </div>
  );
};

export default RoomPage;
