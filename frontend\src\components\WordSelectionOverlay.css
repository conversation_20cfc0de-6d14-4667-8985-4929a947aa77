.word-selection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  color: white;
  font-family: 'Arial', sans-serif;
}

.word-selection-timer {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 20px;
  border: 2px solid #fff;
}

.timer-text {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
}

.word-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  margin: 40px 0;
}

.word-option {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid #fff;
  border-radius: 15px;
  padding: 20px 40px;
  min-width: 200px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  overflow: hidden;
}

.word-option:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.word-option.selecting {
  transform: scale(1.1);
  border-color: #00ff88;
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.word-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.word-text {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.selection-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #00ff88, #00cc6a);
  transition: width 0.1s linear;
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.pinch-instruction {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5px;
  font-style: italic;
}

.word-selection-instructions {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px 25px;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.word-selection-instructions p {
  margin: 5px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

/* Animation for word selection */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 255, 136, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 136, 0);
  }
}

.word-option.selecting {
  animation: pulse 1s infinite;
}

/* Responsive design */
@media (max-width: 768px) {
  .word-option {
    min-width: 150px;
    padding: 15px 30px;
  }
  
  .word-text {
    font-size: 20px;
  }
  
  .timer-text {
    font-size: 16px;
  }
  
  .word-selection-instructions {
    bottom: 20px;
    padding: 10px 20px;
  }
  
  .word-selection-instructions p {
    font-size: 12px;
  }
}
