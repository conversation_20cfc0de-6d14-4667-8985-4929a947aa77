import React, { useRef, useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import './DrawerViewComponent.css';

/**
 * DrawerViewComponent - Shows the drawer's camera feed and drawing canvas for non-drawer players
 * This component receives drawing data and displays it in real-time
 *
 * NEW FEATURE: Can place DOM elements at exact Three.js canvas positions
 *
 * Usage from parent component:
 * const drawerViewRef = useRef();
 *
 * // Place DOM points at Three.js positions
 * const points = [{ x: 100, y: 200, color: 'ff0000', thickness: 8 }];
 * drawerViewRef.current?.placeDOMPointsAtThreeJSPositions(points);
 *
 * // Clear all DOM points
 * drawerViewRef.current?.clearDOMPoints();
 *
 * // Get current DOM points
 * const currentPoints = drawerViewRef.current?.getDOMPoints();
 */
const DrawerViewComponent = forwardRef(({
  drawerStream,
  drawingData = [],
  currentWord,
  drawerName,
  width = 1280,
  height = 960
}, ref) => {
  const canvasRef = useRef(null);
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const [domPoints, setDomPoints] = useState([]);

  // Set up video stream
  useEffect(() => {
    if (drawerStream && videoRef.current) {
      console.log('🎥 Setting drawer stream to video element');
      videoRef.current.srcObject = drawerStream;
      videoRef.current.onloadedmetadata = () => {
        videoRef.current.play();
        setIsVideoReady(true);
      };
    }

    return () => {
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    };
  }, [drawerStream]);

  // Draw the drawing data on canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !drawingData.length) return;

    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw all the drawing data
    drawingData.forEach(stroke => {
      if (stroke.type === 'draw' && stroke.points && stroke.points.length > 1) {
        ctx.beginPath();
        ctx.strokeStyle = stroke.color || '#000000';
        ctx.lineWidth = stroke.thickness || 4;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        
        // Draw the stroke
        ctx.moveTo(stroke.points[0].x, stroke.points[0].y);
        for (let i = 1; i < stroke.points.length; i++) {
          ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
        }
        ctx.stroke();
      } else if (stroke.type === 'erase' && stroke.points && stroke.points.length > 0) {
        // Handle erasing
        ctx.globalCompositeOperation = 'destination-out';
        ctx.beginPath();
        ctx.lineWidth = stroke.thickness || 20;
        ctx.lineCap = 'round';
        
        stroke.points.forEach(point => {
          ctx.beginPath();
          ctx.arc(point.x, point.y, (stroke.thickness || 20) / 2, 0, Math.PI * 2);
          ctx.fill();
        });
        
        ctx.globalCompositeOperation = 'source-over';
      }
    });
  }, [drawingData]);

  /**
   * Function to place DOM elements at exact Three.js canvas positions
   * @param {Array} points - Array of point objects with Three.js coordinates
   * Each point should have: { x, y, z, color, thickness, id }
   *
   * Example usage:
   * const points = [
   *   { x: 100, y: 200, z: 0, color: 'ff0000', thickness: 8, id: 'point1' },
   *   { x: 150, y: 250, z: 5, color: '00ff00', thickness: 12, id: 'point2' }
   * ];
   * drawerViewRef.current.placeDOMPointsAtThreeJSPositions(points);
   */
  const placeDOMPointsAtThreeJSPositions = (points) => {
    if (!containerRef.current || !points || !Array.isArray(points)) {
      console.warn('🎯 placeDOMPointsAtThreeJSPositions: Invalid container or points');
      return;
    }

    // Clear existing DOM points
    const existingPoints = containerRef.current.querySelectorAll('.dom-drawing-point');
    existingPoints.forEach(point => point.remove());

    // Create new DOM elements for each point
    const newDomPoints = points.map((point, index) => {
      if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
        console.warn('🎯 Invalid point data at index', index, ':', point);
        return null;
      }

      // Create DOM element
      const pointElement = document.createElement('div');
      pointElement.className = 'dom-drawing-point';
      pointElement.id = point.id || `point-${index}`;

      // Calculate size from thickness
      const size = point.thickness || 4;
      const radius = size / 2;

      // Apply styles for exact positioning and appearance
      pointElement.style.cssText = `
        position: absolute;
        left: ${point.x - radius}px;
        top: ${point.y - radius}px;
        width: ${size}px;
        height: ${size}px;
        background-color: ${point.color ? `#${point.color}` : '#000000'};
        border-radius: 50%;
        pointer-events: none;
        z-index: 10;
        transform: translateZ(${point.z || 0}px);
      `;

      // Add to container
      containerRef.current.appendChild(pointElement);

      return {
        element: pointElement,
        data: point
      };
    }).filter(Boolean);

    // Update state
    setDomPoints(newDomPoints);

    console.log('🎯 Placed', newDomPoints.length, 'DOM points at Three.js positions');
    return newDomPoints;
  };

  // Example usage: Convert drawingData to DOM points when it changes
  useEffect(() => {
    if (drawingData && drawingData.length > 0) {
      // Convert drawing data to point format if needed
      const points = [];

      drawingData.forEach((stroke, strokeIndex) => {
        if (stroke.points && Array.isArray(stroke.points)) {
          stroke.points.forEach((point, pointIndex) => {
            points.push({
              x: point.x,
              y: point.y,
              z: point.z || 0,
              color: stroke.color?.replace('#', '') || '000000',
              thickness: stroke.thickness || 4,
              id: `stroke-${strokeIndex}-point-${pointIndex}`
            });
          });
        }
      });

      // Uncomment the line below to use DOM points instead of canvas drawing
      // placeDOMPointsAtThreeJSPositions(points);

      // Example: Test with some sample points (uncomment to test)
      // const testPoints = [
      //   { x: 100, y: 100, z: 0, color: 'ff0000', thickness: 10, id: 'test1' },
      //   { x: 200, y: 150, z: 2, color: '00ff00', thickness: 15, id: 'test2' },
      //   { x: 300, y: 200, z: 4, color: '0000ff', thickness: 20, id: 'test3' }
      // ];
      // placeDOMPointsAtThreeJSPositions(testPoints);
    }
  }, [drawingData]);

  // Expose the placeDOMPointsAtThreeJSPositions function via ref
  useImperativeHandle(ref, () => ({
    placeDOMPointsAtThreeJSPositions,
    clearDOMPoints: () => {
      const existingPoints = containerRef.current?.querySelectorAll('.dom-drawing-point');
      existingPoints?.forEach(point => point.remove());
      setDomPoints([]);
    },
    getDOMPoints: () => domPoints
  }), [domPoints]);

  return (
    <div
      ref={containerRef}
      className="drawer-view-container"
      style={{ width, height, position: 'relative' }}
    >
      {/* Video background */}
      <video
        ref={videoRef}
        className="drawer-video"
        autoPlay
        playsInline
        muted
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1
        }}
      />

      {/* Drawing canvas overlay */}
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="drawer-canvas"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 2,
          pointerEvents: 'none'
        }}
      />

      {/* UI Overlays */}
      <div className="drawer-ui-overlay" style={{ zIndex: 3 }}>

        {/* Drawer name */}
        <div className="drawer-name">
          🎨 {drawerName} is drawing
        </div>

        {/* Current word (for drawer only - this won't show for guessers) */}
        {currentWord && (
          <div className="current-word-display">
            {currentWord}
          </div>
        )}
      </div>

      {/* Loading state */}
      {!isVideoReady && (
        <div className="drawer-loading">
          <div className="loading-spinner"></div>
          <div className="loading-text">Connecting to {drawerName}'s camera...</div>
        </div>
      )}
    </div>
  );
});

DrawerViewComponent.displayName = 'DrawerViewComponent';

export default DrawerViewComponent;
