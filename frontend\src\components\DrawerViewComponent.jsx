import React, { useRef, useEffect, useState } from 'react';
import './DrawerViewComponent.css';

/**
 * DrawerViewComponent - Shows the drawer's camera feed and drawing canvas for non-drawer players
 * This component receives drawing data and displays it in real-time
 */
const DrawerViewComponent = ({
  drawerStream,
  drawingData = [],
  currentWord,
  timeLeft,
  drawerName,
  width = 1280,
  height = 960
}) => {
  const canvasRef = useRef(null);
  const videoRef = useRef(null);
  const [isVideoReady, setIsVideoReady] = useState(false);

  // Set up video stream
  useEffect(() => {
    if (drawerStream && videoRef.current) {
      console.log('🎥 Setting drawer stream to video element');
      videoRef.current.srcObject = drawerStream;
      videoRef.current.onloadedmetadata = () => {
        videoRef.current.play();
        setIsVideoReady(true);
      };
    }

    return () => {
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    };
  }, [drawerStream]);

  // Draw the drawing data on canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (!drawingData || !drawingData.length) {
      console.log('🎨 No drawing data to render');
      return;
    }

    console.log('🎨 Rendering drawing data:', drawingData.length, 'points');

    // Draw all the drawing points as circles (dots)
    drawingData.forEach((point, index) => {
      if (point && typeof point.x === 'number' && typeof point.y === 'number') {
        ctx.beginPath();
        ctx.fillStyle = point.color ? `#${point.color}` : '#000000';
        const radius = (point.thickness || 4) / 2;
        ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
        ctx.fill();
      } else {
        console.warn('🎨 Invalid drawing point at index', index, ':', point);
      }
    });
  }, [drawingData]);

  return (
    <div className="drawer-view-container" style={{
      width: '100%',
      height: '100%',
      position: 'relative',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      {/* Video background - centered and fitted */}
      <video
        ref={videoRef}
        className="drawer-video"
        autoPlay
        playsInline
        muted
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1
        }}
      />

      {/* Drawing canvas overlay - same size as video */}
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="drawer-canvas"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 2,
          pointerEvents: 'none',
          objectFit: 'contain'
        }}
      />

      {/* UI Overlays */}
      <div className="drawer-ui-overlay" style={{ zIndex: 3 }}>
        {/* Drawer name */}
        <div className="drawer-name">
          🎨 {drawerName} is drawing
        </div>
      </div>

      {/* Loading state */}
      {!isVideoReady && (
        <div className="drawer-loading">
          <div className="loading-spinner"></div>
          <div className="loading-text">Connecting to {drawerName}'s camera...</div>
        </div>
      )}
    </div>
  );
};

export default DrawerViewComponent;
