import React, { useRef, useEffect, useState } from 'react';
import * as THREE from 'three';
import './DrawerViewComponent.css';

/**
 * DrawerViewComponent - Shows the drawer's camera feed and drawing canvas for non-drawer players
 * This component receives drawing data and displays it in real-time
 *
 * NEW FEATURE: Can place DOM elements at exact Three.js canvas positions
 *
 * Usage from parent component:
 * const drawerViewRef = useRef();
 *
 * // Place DOM points at Three.js positions
 * const points = [{ x: 100, y: 200, color: 'ff0000', thickness: 8 }];
 * drawerViewRef.current?.placeDOMPointsAtThreeJSPositions(points);
 *
 * // Clear all DOM points
 * drawerViewRef.current?.clearDOMPoints();
 *
 * // Get current DOM points
 * const currentPoints = drawerViewRef.current?.getDOMPoints();
 */
const DrawerViewComponent = ({
  drawerStream,
  drawingData = [],
  currentWord,
  drawerName,
  width = 1280,
  height = 960
}) => {
  const canvasRef = useRef(null);
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const threeCanvasRef = useRef(null);
  const [isVideoReady, setIsVideoReady] = useState(false);
  const [domPoints, setDomPoints] = useState([]);

  // Three.js refs - copied from HandGestureDetector
  const threeSceneRef = useRef(null);
  const threeCameraRef = useRef(null);
  const threeRendererRef = useRef(null);
  const animationFrameRef = useRef(null);
  const drawingDotsRef = useRef([]);

  // Set up video stream
  useEffect(() => {
    if (drawerStream && videoRef.current) {
      console.log('🎥 Setting drawer stream to video element');
      videoRef.current.srcObject = drawerStream;
      videoRef.current.onloadedmetadata = () => {
        videoRef.current.play();
        setIsVideoReady(true);
      };
    }

    return () => {
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    };
  }, [drawerStream]);

  // Setup Three.js overlay - copied exactly from HandGestureDetector
  useEffect(() => {
    if (!threeCanvasRef.current) return;

    // Setup Three.js scene, camera, renderer
    const scene = new THREE.Scene();
    const camera = new THREE.OrthographicCamera(0, width, height, 0, -10, 10);
    const renderer = new THREE.WebGLRenderer({ canvas: threeCanvasRef.current, alpha: true });
    renderer.setClearColor(0x000000, 0); // transparent
    renderer.setSize(width, height);
    threeSceneRef.current = scene;
    threeCameraRef.current = camera;
    threeRendererRef.current = renderer;

    // Animation loop
    const animate = () => {
      renderer.render(scene, camera);
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    animate();

    // Cleanup
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      renderer.dispose();
    };
  }, [width, height]);

  // Draw the drawing data on canvas
  // Canvas drawing is now disabled - we use Three.js points instead
  // The drawing data is already in Three.js point format from HandGestureDetector
  // and is processed in the useEffect below to place DOM points at Three.js positions

  /**
   * Function to place Three.js meshes at exact positions - copied from HandGestureDetector
   * @param {Array} points - Array of point objects with Three.js coordinates
   * Each point should have: { x, y, z, color, thickness, id }
   *
   * Example usage:
   * const points = [
   *   { x: 100, y: 200, z: 0, color: 'ff0000', thickness: 8, id: 'point1' },
   *   { x: 150, y: 250, z: 5, color: '00ff00', thickness: 12, id: 'point2' }
   * ];
   * placeDOMPointsAtThreeJSPositions(points);
   */
  const placeDOMPointsAtThreeJSPositions = (points) => {
    if (!threeSceneRef.current || !points || !Array.isArray(points)) {
      console.warn('🎯 placeDOMPointsAtThreeJSPositions: Invalid Three.js scene or points');
      return;
    }

    // Clear existing drawing dots efficiently
    drawingDotsRef.current.forEach(dot => {
      if (dot.mesh) {
        threeSceneRef.current.remove(dot.mesh);
        dot.mesh.geometry.dispose();
        dot.mesh.material.dispose();
      }
    });
    drawingDotsRef.current = [];

    // Batch create new Three.js meshes for better performance
    const newDots = [];
    for (let i = 0; i < points.length; i++) {
      const point = points[i];

      if (!point || typeof point.x !== 'number' || typeof point.y !== 'number') {
        continue; // Skip invalid points silently for performance
      }

      // Create Three.js mesh exactly like HandGestureDetector does
      const radius = (point.thickness || 4) / 2;
      const dotGeom = new THREE.CircleGeometry(radius, 14);
      const colorHex = new THREE.Color(point.color ? `#${point.color}` : '#000000');
      const dotMat = new THREE.MeshBasicMaterial({ color: colorHex });
      const dotMesh = new THREE.Mesh(dotGeom, dotMat);

      // Set position exactly like HandGestureDetector
      dotMesh.position.set(point.x, point.y, point.z || 0);

      // Add to scene
      threeSceneRef.current.add(dotMesh);

      // Store in drawingDotsRef exactly like HandGestureDetector
      const dotData = {
        x: point.x,
        y: point.y,
        z: point.z || 0,
        mesh: dotMesh
      };
      drawingDotsRef.current.push(dotData);
      newDots.push(dotData);
    }

    // Only log significant updates to reduce console spam
    if (newDots.length > 0 && newDots.length % 10 === 0) {
      console.log('🎯 Placed', newDots.length, 'Three.js meshes');
    }

    return newDots;
  };

  // Convert normalized drawingData to local screen coordinates and display as Three.js points
  // Optimized for frequent updates from real-time drawing transmission
  const lastDrawingDataLengthRef = useRef(0);
  const updateCountRef = useRef(0);
  useEffect(() => {
    updateCountRef.current++;

    // Handle both old format (array) and new format (object with points array)
    let points = [];
    let sourceCanvasWidth = width;
    let sourceCanvasHeight = height;
    let isNormalizedFormat = false;

    if (drawingData) {
      if (Array.isArray(drawingData)) {
        // Old format: direct array of points (backward compatibility)
        points = drawingData;
        console.log('🔄 Using legacy absolute coordinate format');
      } else if (drawingData.points && Array.isArray(drawingData.points)) {
        // New format: normalized coordinates with source canvas dimensions
        points = drawingData.points;
        sourceCanvasWidth = drawingData.canvasWidth || width;
        sourceCanvasHeight = drawingData.canvasHeight || height;
        isNormalizedFormat = drawingData.format === 'normalized';
        console.log('🎯 Using normalized coordinate format, scaling from', sourceCanvasWidth + 'x' + sourceCanvasHeight, 'to', width + 'x' + height);
      }
    }

    // Log every 10 updates to monitor performance
    if (updateCountRef.current % 10 === 0) {
      console.log('🎨 DrawerViewComponent: Received update #' + updateCountRef.current + ' with', points.length, 'points');
    }

    if (points.length > 0) {
      // Clear existing points first for complete sync
      clearThreeJSPoints();

      // Scale coordinates to local screen size
      const formattedPoints = points.map((point, index) => {
        let scaledX, scaledY, scaledZ, scaledThickness;

        if (isNormalizedFormat) {
          // New format: Scale normalized coordinates (0-1 range) to local canvas size
          scaledX = point.x * width;
          scaledY = point.y * height;
          scaledZ = (point.z || 0) * width; // Scale z proportionally
          scaledThickness = (point.thickness || 0.003125) * width; // Default thickness: 4/1280 = 0.003125
        } else {
          // Legacy format: Check if coordinates look normalized or absolute
          const looksNormalized = point.x <= 1 && point.y <= 1 && point.x >= 0 && point.y >= 0;

          if (looksNormalized) {
            // Treat as normalized coordinates
            scaledX = point.x * width;
            scaledY = point.y * height;
            scaledZ = (point.z || 0) * width;
            scaledThickness = (point.thickness || 0.003125) * width;
          } else {
            // Legacy absolute coordinates - use as-is (backward compatibility)
            scaledX = point.x;
            scaledY = point.y;
            scaledZ = point.z || 0;
            scaledThickness = point.thickness || 4;
          }
        }

        return {
          x: scaledX,
          y: scaledY,
          z: scaledZ,
          color: point.color || 'ffffff',
          thickness: scaledThickness,
          id: `point-${index}`
        };
      });

      console.log('🎯 Scaling', formattedPoints.length, 'points from', sourceCanvasWidth + 'x' + sourceCanvasHeight, 'to', width + 'x' + height);

      // Place all points at once for better performance
      placeDOMPointsAtThreeJSPositions(formattedPoints);
    } else {
      // Clear points if no drawing data
      clearThreeJSPoints();
    }
  }, [drawingData, width, height]);

  // Function to clear Three.js points (can be called internally)
  const clearThreeJSPoints = () => {
    drawingDotsRef.current.forEach(dot => {
      if (dot.mesh) {
        threeSceneRef.current.remove(dot.mesh);
        dot.mesh.geometry.dispose();
        dot.mesh.material.dispose();
      }
    });
    drawingDotsRef.current = [];
    console.log('🎯 Cleared all Three.js points');
  };

  // Function to get current Three.js points
  const getThreeJSPoints = () => drawingDotsRef.current;

  return (
    <div
      ref={containerRef}
      className="drawer-view-container"
      style={{ width, height, position: 'relative' }}
    >
      {/* Three.js overlay canvas - copied from HandGestureDetector */}
      <canvas
        ref={threeCanvasRef}
        className="three-canvas"
        style={{ position: 'absolute', left: 0, top: 0, width: '100%', height: '100%', zIndex: 5 }}
        width={width}
        height={height}
      />

      {/* Video background */}
      <video
        ref={videoRef}
        className="drawer-video"
        autoPlay
        playsInline
        muted
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1
        }}
      />

      {/* Drawing canvas overlay */}
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="drawer-canvas"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 2,
          pointerEvents: 'none'
        }}
      />

      {/* UI Overlays */}
      <div className="drawer-ui-overlay" style={{ zIndex: 3 }}>

        {/* Drawer name */}
        <div className="drawer-name">
          🎨 {drawerName} is drawing
        </div>

      </div>

      {/* Loading state */}
      {!isVideoReady && (
        <div className="drawer-loading">
          <div className="loading-spinner"></div>
          <div className="loading-text">Connecting to {drawerName}'s camera...</div>
        </div>
      )}
    </div>
  );
};

export default DrawerViewComponent;
