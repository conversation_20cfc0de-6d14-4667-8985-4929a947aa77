import React, { useRef, useEffect, useState } from 'react';
import './DrawerViewComponent.css';

/**
 * DrawerViewComponent - Shows the drawer's camera feed and drawing canvas for non-drawer players
 * This component receives drawing data and displays it in real-time
 */
const DrawerViewComponent = ({
  drawerStream,
  drawingData = [],
  currentWord,
  timeLeft,
  drawerName,
  width = 1280,
  height = 960
}) => {
  const canvasRef = useRef(null);
  const videoRef = useRef(null);
  const [isVideoReady, setIsVideoReady] = useState(false);

  // Set up video stream
  useEffect(() => {
    if (drawerStream && videoRef.current) {
      console.log('🎥 Setting drawer stream to video element');
      videoRef.current.srcObject = drawerStream;
      videoRef.current.onloadedmetadata = () => {
        videoRef.current.play();
        setIsVideoReady(true);
      };
    }

    return () => {
      if (videoRef.current) {
        videoRef.current.srcObject = null;
      }
    };
  }, [drawerStream]);

  // Draw the drawing data on canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !drawingData.length) return;

    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw all the drawing data
    drawingData.forEach(stroke => {
      if (stroke.type === 'draw' && stroke.points && stroke.points.length > 1) {
        ctx.beginPath();
        ctx.strokeStyle = stroke.color || '#000000';
        ctx.lineWidth = stroke.thickness || 4;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        
        // Draw the stroke
        ctx.moveTo(stroke.points[0].x, stroke.points[0].y);
        for (let i = 1; i < stroke.points.length; i++) {
          ctx.lineTo(stroke.points[i].x, stroke.points[i].y);
        }
        ctx.stroke();
      } else if (stroke.type === 'erase' && stroke.points && stroke.points.length > 0) {
        // Handle erasing
        ctx.globalCompositeOperation = 'destination-out';
        ctx.beginPath();
        ctx.lineWidth = stroke.thickness || 20;
        ctx.lineCap = 'round';
        
        stroke.points.forEach(point => {
          ctx.beginPath();
          ctx.arc(point.x, point.y, (stroke.thickness || 20) / 2, 0, Math.PI * 2);
          ctx.fill();
        });
        
        ctx.globalCompositeOperation = 'source-over';
      }
    });
  }, [drawingData]);

  return (
    <div className="drawer-view-container" style={{ width, height }}>
      {/* Video background */}
      <video
        ref={videoRef}
        className="drawer-video"
        autoPlay
        playsInline
        muted
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1
        }}
      />

      {/* Drawing canvas overlay */}
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="drawer-canvas"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 2,
          pointerEvents: 'none'
        }}
      />

      {/* UI Overlays */}
      <div className="drawer-ui-overlay" style={{ zIndex: 3 }}>
        {/* Timer */}
        <div className="drawer-timer">
          <span className="timer-icon">⏱️</span>
          <span className="timer-value">
            {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}
          </span>
        </div>

        {/* Drawer name */}
        <div className="drawer-name">
          🎨 {drawerName} is drawing
        </div>

        {/* Current word (for drawer only - this won't show for guessers) */}
        {currentWord && (
          <div className="current-word-display">
            {currentWord}
          </div>
        )}
      </div>

      {/* Loading state */}
      {!isVideoReady && (
        <div className="drawer-loading">
          <div className="loading-spinner"></div>
          <div className="loading-text">Connecting to {drawerName}'s camera...</div>
        </div>
      )}
    </div>
  );
};

export default DrawerViewComponent;
