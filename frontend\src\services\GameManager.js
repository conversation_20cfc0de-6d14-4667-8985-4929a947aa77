import { extractWordsFromCSV, getRandomWords} from '../utils/wordExtractor.js';

/**
 * Simple EventEmitter implementation for browser compatibility
 */
class SimpleEventEmitter {
  constructor() {
    this.events = {};
  }

  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  off(event, listener) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(l => l !== listener);
  }

  emit(event, ...args) {
    if (!this.events[event]) return;
    this.events[event].forEach(listener => {
      try {
        listener(...args);
      } catch (error) {
        console.error('Error in event listener:', error);
      }
    });
  }

  removeAllListeners(event) {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}

/**
 * Centralized Game Manager - Only runs on the room owner
 * All other players receive synced state updates via P2PManager
 */
class GameManager extends SimpleEventEmitter {
  constructor(p2pManager) {
    super();
    this.p2pManager = p2pManager;
    this.isActive = false; // Only true for the owner
    this.gameState = {
      status: 'waiting', // 'waiting', 'playing', 'finished'
      currentRound: 1,
      maxRounds: 3,
      drawingOrder: [],
      currentDrawer: null,
      currentWord: null,
      currentWordOptions: [], // Available word choices for drawer
      timeLeft: 0,
      scores: new Map(), // peerId -> score
      roundStartTime: null,
      drawingData: [],
      guesses: [],
      gameId: null, // Unique identifier for this game session
      lastUpdate: Date.now()
    };

    // Load words from CSV file
    this.words = extractWordsFromCSV();
    this.drawTimer = null;

    // Set initial active state based on ownership
    this.isActive = this.p2pManager.isOwner;

    console.log('🎮 GameManager created:', {
      isActive: this.isActive,
      isOwner: this.p2pManager.isOwner,
      peerId: this.p2pManager.peerId
    });
  }

  /**
   * Activate this GameManager (called when becoming owner)
   */
  activate() {
    if (this.isActive) return;

    console.log('🎮 Activating GameManager - becoming authoritative');
    this.isActive = true;

    // Generate new game ID to mark this as a new authoritative session
    this.gameState.gameId = `game_${Date.now()}_${this.p2pManager.peerId}`;
    this.gameState.lastUpdate = Date.now();

    // Broadcast current state to all peers
    this.broadcastGameState();

    this.emit('gameManagerActivated');
  }

  /**
   * Deactivate this GameManager (called when losing ownership)
   */
  deactivate() {
    if (!this.isActive) return;

    console.log('🎮 Deactivating GameManager - no longer authoritative');
    this.isActive = false;

    // Stop any running timers
    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }

    this.emit('gameManagerDeactivated');
  }

  /**
   * Sync game state from authoritative source (for non-owners)
   */
  syncGameState(authoritativeGameState) {
    if (this.isActive) {
      console.warn('🎮 Ignoring sync - this GameManager is authoritative');
      return;
    }

    console.log('🎮 Syncing game state from authoritative source');

    // Preserve the scores Map structure
    const newGameState = { ...authoritativeGameState };
    if (authoritativeGameState.scores) {
      newGameState.scores = new Map(Object.entries(authoritativeGameState.scores));
    }

    this.gameState = newGameState;
    this.emit('gameStateUpdated', this.gameState);

    // Emit specific events based on state
    if (this.gameState.status === 'playing' && this.gameState.currentDrawer) {
      this.emit('roundStarted', {
        round: this.gameState.currentRound,
        drawer: this.gameState.currentDrawer,
        word: this.gameState.currentWord,
        timeLeft: this.gameState.timeLeft
      });
    }

    if (this.gameState.timeLeft > 0) {
      this.emit('timerUpdate', this.gameState.timeLeft);
    }
  }

  /**
   * Start a new game (only for active/authoritative GameManager)
   */
  startGame(settings) {
    if (!this.isActive) {
      console.warn('❌ GameManager not active, cannot start game');
      return;
    }

    console.log('🎮 Starting new game', {
      isActive: this.isActive,
      settings,
      currentStatus: this.gameState.status
    });

    // Generate new game session
    this.gameState.gameId = `game_${Date.now()}_${this.p2pManager.peerId}`;
    this.gameState.status = 'playing';
    this.gameState.currentRound = 1;
    this.gameState.maxRounds = settings.rounds || 3;
    this.gameState.lastUpdate = Date.now();

    // Reset scores
    this.gameState.scores.clear();
    const players = this.p2pManager.getRoomState().players;
    console.log('🎮 Resetting scores for players:', players.map(p => p.name));
    players.forEach(player => {
      this.gameState.scores.set(player.peerId, 0);
    });

    // Clear previous game data
    this.gameState.drawingData = [];
    this.gameState.guesses = [];

    console.log('🎮 Starting new round...');
    this.startNewRound();
  }

  /**
   * Start a new round (only for active/authoritative GameManager)
   */
  startNewRound() {
    if (!this.isActive) {
      console.warn('❌ GameManager not active, cannot start new round');
      return;
    }

    console.log('🎮 Starting new round');

    const players = this.p2pManager.getRoomState().players;
    console.log('🎮 Players in room:', players.map(p => p.name));

    if (players.length === 0) {
      console.warn('❌ No players in room, cannot start round');
      return;
    }

    // Select next drawer (round-robin)
    const drawerIndex = (this.gameState.currentRound - 1) % players.length;
    this.gameState.currentDrawer = players[drawerIndex];
    console.log('🎨 Selected drawer:', this.gameState.currentDrawer.name);

    // Select random word options for drawer to choose from
    this.gameState.currentWordOptions = getRandomWords(this.words, 3);
    this.gameState.currentWord = null; // Will be set when drawer chooses
    console.log('📝 Word options:', this.gameState.currentWordOptions);

    // Set timer
    const drawTime = this.p2pManager.getRoomState().settings.drawTime || 80;
    this.gameState.timeLeft = drawTime;
    this.gameState.roundStartTime = Date.now();
    this.gameState.lastUpdate = Date.now();
    console.log('⏰ Timer set to:', drawTime, 'seconds');

    // Clear previous round data
    this.gameState.drawingData = [];
    this.gameState.guesses = [];

    // Broadcast round start
    console.log('📡 Broadcasting game state...');
    this.broadcastGameState();

    // Start countdown timer
    console.log('⏰ Starting draw timer...');
    this.startDrawTimer();

    const roundData = {
      round: this.gameState.currentRound,
      drawer: this.gameState.currentDrawer,
      wordOptions: this.gameState.currentWordOptions,
      timeLeft: this.gameState.timeLeft
    };

    console.log('🎮 Emitting roundStarted event:', roundData);
    this.emit('roundStarted', roundData);
  }

  /**
   * Start the drawing timer (only for active GameManager)
   */
  startDrawTimer() {
    if (!this.isActive) return;

    if (this.drawTimer) {
      clearInterval(this.drawTimer);
    }

    this.drawTimer = setInterval(() => {
      this.gameState.timeLeft--;
      this.gameState.lastUpdate = Date.now();

      if (this.gameState.timeLeft <= 0) {
        this.endRound();
      } else {
        this.emit('timerUpdate', this.gameState.timeLeft);
        // Broadcast timer updates every 5 seconds to keep everyone in sync
        if (this.gameState.timeLeft % 5 === 0) {
          this.broadcastGameState();
        }
      }
    }, 1000);
  }

  /**
   * End the current round (only for active GameManager)
   */
  endRound() {
    if (!this.isActive) return;

    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }

    this.gameState.lastUpdate = Date.now();

    this.emit('roundEnded', {
      round: this.gameState.currentRound,
      word: this.gameState.currentWord,
      scores: Object.fromEntries(this.gameState.scores)
    });

    // Broadcast the round end state
    this.broadcastGameState();

    // Check if game should end
    if (this.gameState.currentRound >= this.gameState.maxRounds) {
      this.endGame();
    } else {
      // Prepare next round
      this.gameState.currentRound++;
      setTimeout(() => {
        this.startNewRound();
      }, 3000); // 3 second break between rounds
    }
  }

  /**
   * End the game (only for active GameManager)
   */
  endGame() {
    if (!this.isActive) return;

    this.gameState.status = 'finished';
    this.gameState.lastUpdate = Date.now();

    // Calculate final rankings
    const finalScores = Array.from(this.gameState.scores.entries())
      .map(([peerId, score]) => {
        const player = this.p2pManager.getRoomState().players.find(p => p.peerId === peerId);
        return { peerId, player, score };
      })
      .sort((a, b) => b.score - a.score);

    this.broadcastGameState();

    this.emit('gameEnded', {
      finalScores,
      winner: finalScores[0]
    });
  }

  /**
   * Handle a player's guess (only for active GameManager)
   */
  submitGuess(peerId, guess) {
    if (!this.isActive) return;
    if (this.gameState.status !== 'playing') return;
    if (peerId === this.gameState.currentDrawer?.peerId) return; // Drawer can't guess

    const player = this.p2pManager.getRoomState().players.find(p => p.peerId === peerId);
    if (!player) return;

    const guessData = {
      peerId,
      playerName: player.name,
      guess: guess.trim().toLowerCase(),
      timestamp: Date.now(),
      isCorrect: false
    };

    // Check if guess is correct
    if (this.gameState.currentWord &&
        guessData.guess === this.gameState.currentWord.toLowerCase()) {
      guessData.isCorrect = true;

      // Award points
      const timeBonus = Math.max(0, this.gameState.timeLeft);
      const points = 100 + timeBonus;

      const currentScore = this.gameState.scores.get(peerId) || 0;
      this.gameState.scores.set(peerId, currentScore + points);

      // Award points to drawer too
      const drawerScore = this.gameState.scores.get(this.gameState.currentDrawer.peerId) || 0;
      this.gameState.scores.set(this.gameState.currentDrawer.peerId, drawerScore + 50);

      this.emit('correctGuess', { player, points, timeBonus });

      // End round early on correct guess
      setTimeout(() => {
        this.endRound();
      }, 2000);
    }

    this.gameState.guesses.push(guessData);
    this.gameState.lastUpdate = Date.now();
    this.emit('newGuess', guessData);

    // Broadcast updated game state
    this.broadcastGameState();
  }

  /**
   * Handle word selection by drawer (only for active GameManager)
   */
  selectWord(peerId, selectedWord) {
    if (!this.isActive) return;
    if (this.gameState.status !== 'playing') return;
    if (peerId !== this.gameState.currentDrawer?.peerId) return; // Only drawer can select
    if (!this.gameState.currentWordOptions.includes(selectedWord)) return; // Must be valid option

    this.gameState.currentWord = selectedWord;
    this.gameState.lastUpdate = Date.now();

    console.log('📝 Word selected:', selectedWord);

    // Broadcast updated game state
    this.broadcastGameState();

    this.emit('wordSelected', { word: selectedWord, drawer: this.gameState.currentDrawer });
  }

  /**
   * Handle drawing data from the current drawer (only for active GameManager)
   */
  handleDrawingData(drawingData) {
    if (!this.isActive) return;
    if (this.gameState.status !== 'playing') return;

    this.gameState.drawingData.push({
      ...drawingData,
      timestamp: Date.now()
    });

    this.gameState.lastUpdate = Date.now();
    this.emit('drawingUpdate', drawingData);

    // Broadcast drawing data to all peers
    this.broadcastGameState();
  }

  /**
   * Handle player disconnection (only for active GameManager)
   */
  handlePlayerDisconnection(peerId) {
    if (!this.isActive) return;

    console.log('🎮 Handling player disconnection:', peerId);

    // If the current drawer disconnects, end the round
    if (this.gameState.currentDrawer?.peerId === peerId && this.gameState.status === 'playing') {
      console.log('🎮 Current drawer disconnected, ending round');
      this.endRound();
    }

    // Remove player's score
    this.gameState.scores.delete(peerId);
    this.gameState.lastUpdate = Date.now();

    this.emit('playerDisconnected', peerId);

    // Broadcast updated game state
    this.broadcastGameState();
  }

  /**
   * Broadcast current game state to all peers (only for active GameManager)
   */
  broadcastGameState() {
    if (!this.isActive) return;

    // Convert Map to Object for serialization
    const gameStateForBroadcast = {
      ...this.gameState,
      scores: Object.fromEntries(this.gameState.scores)
    };

    this.p2pManager.broadcastGameState(gameStateForBroadcast);
  }

  /**
   * Get current game state
   */
  getGameState() {
    return {
      ...this.gameState,
      scores: Object.fromEntries(this.gameState.scores)
    };
  }

  /**
   * Get player scores
   */
  getScores() {
    return Object.fromEntries(this.gameState.scores);
  }

  /**
   * Check if current user is the drawer
   */
  isCurrentDrawer() {
    return this.gameState.currentDrawer?.peerId === this.p2pManager.peerId;
  }

  /**
   * Check if this GameManager is active/authoritative
   */
  isActiveManager() {
    return this.isActive;
  }

  /**
   * Reset game to waiting state (only for active GameManager)
   */
  resetGame() {
    if (!this.isActive) return;

    console.log('🎮 Resetting game to waiting state');

    // Stop any running timers
    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }

    // Reset game state
    this.gameState.status = 'waiting';
    this.gameState.currentRound = 1;
    this.gameState.currentDrawer = null;
    this.gameState.currentWord = null;
    this.gameState.currentWordOptions = [];
    this.gameState.timeLeft = 0;
    this.gameState.drawingData = [];
    this.gameState.guesses = [];
    this.gameState.lastUpdate = Date.now();

    // Keep scores but could reset them too if desired
    // this.gameState.scores.clear();

    this.broadcastGameState();
    this.emit('gameReset');
  }

  /**
   * Cleanup
   */
  cleanup() {
    console.log('🎮 Cleaning up GameManager');

    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }

    this.isActive = false;
    this.removeAllListeners();
  }
}

export default GameManager;
