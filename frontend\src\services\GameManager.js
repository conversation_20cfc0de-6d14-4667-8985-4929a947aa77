import { extractWordsFromCSV, getRandomWords} from '../utils/wordExtractor.js';

/**
 * Simple EventEmitter implementation for browser compatibility
 */
class SimpleEventEmitter {
  constructor() {
    this.events = {};
  }

  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
  }

  off(event, listener) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(l => l !== listener);
  }

  emit(event, ...args) {
    if (!this.events[event]) return;
    this.events[event].forEach(listener => {
      try {
        listener(...args);
      } catch (error) {
        console.error('Error in event listener:', error);
      }
    });
  }

  removeAllListeners(event) {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
  }
}

/**
 * Centralized Game Manager - Only runs on the room owner
 * All other players receive synced state updates via P2PManager
 */
class GameManager extends SimpleEventEmitter {
  constructor(p2pManager) {
    super();
    this.p2pManager = p2pManager;
    this.isActive = false; // Only true for the owner
    this.gameState = {
      status: 'waiting', // 'waiting', 'wordSelection', 'playing', 'finished'
      currentRound: 1,
      maxRounds: 3,
      drawingOrder: [], // Randomized order of players for the entire game
      currentDrawerIndex: 0, // Index in drawingOrder array
      currentDrawer: null,
      currentWord: null,
      currentWordOptions: [], // Available word choices for drawer
      wordSelectionTimeLeft: 0, // Time left for word selection (20 seconds)
      timeLeft: 0, // Time left for drawing
      scores: new Map(), // peerId -> score (persistent across disconnections)
      playerHistory: new Map(), // peerId -> { name, lastScore, joinTime } for reconnections
      roundStartTime: null,
      drawingData: [],
      guesses: [],
      gameId: null, // Unique identifier for this game session
      lastUpdate: Date.now()
    };

    // Load words from CSV file
    this.words = extractWordsFromCSV();
    this.drawTimer = null;
    this.wordSelectionTimer = null;

    // Set initial active state based on ownership
    this.isActive = this.p2pManager.isOwner;

    console.log('🎮 GameManager created:', {
      isActive: this.isActive,
      isOwner: this.p2pManager.isOwner,
      peerId: this.p2pManager.peerId
    });
  }

  /**
   * Activate this GameManager (called when becoming owner)
   */
  activate() {
    if (this.isActive) return;

    console.log('🎮 Activating GameManager - becoming authoritative');
    this.isActive = true;

    // Generate new game ID to mark this as a new authoritative session
    this.gameState.gameId = `game_${Date.now()}_${this.p2pManager.peerId}`;
    this.gameState.lastUpdate = Date.now();

    // Broadcast current state to all peers
    this.broadcastGameState();

    this.emit('gameManagerActivated');
  }

  /**
   * Deactivate this GameManager (called when losing ownership)
   */
  deactivate() {
    if (!this.isActive) return;

    console.log('🎮 Deactivating GameManager - no longer authoritative');
    this.isActive = false;

    // Stop any running timers
    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }
    if (this.wordSelectionTimer) {
      clearInterval(this.wordSelectionTimer);
      this.wordSelectionTimer = null;
    }

    this.emit('gameManagerDeactivated');
  }

  /**
   * Sync game state from authoritative source (for non-owners)
   */
  syncGameState(authoritativeGameState) {
    if (this.isActive) {
      console.warn('🎮 Ignoring sync - this GameManager is authoritative');
      return;
    }

    console.log('🎮 Syncing game state from authoritative source');

    // Preserve the scores Map structure
    const newGameState = { ...authoritativeGameState };
    if (authoritativeGameState.scores) {
      newGameState.scores = new Map(Object.entries(authoritativeGameState.scores));
    }

    this.gameState = newGameState;
    this.emit('gameStateUpdated', this.gameState);

    // Emit specific events based on state
    if (this.gameState.status === 'playing' && this.gameState.currentDrawer) {
      this.emit('roundStarted', {
        round: this.gameState.currentRound,
        drawer: this.gameState.currentDrawer,
        word: this.gameState.currentWord,
        timeLeft: this.gameState.timeLeft
      });
    }

    if (this.gameState.timeLeft > 0) {
      this.emit('timerUpdate', this.gameState.timeLeft);
    }
  }

  /**
   * Randomize array using Fisher-Yates shuffle
   */
  shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Create randomized drawing order for the game
   */
  createDrawingOrder(players) {
    console.log('🎲 Creating randomized drawing order for players:', players.map(p => p.name));
    const shuffledPlayers = this.shuffleArray(players);
    console.log('🎲 Randomized order:', shuffledPlayers.map(p => p.name));
    return shuffledPlayers;
  }

  /**
   * Add new player to existing drawing order (for mid-game joins)
   */
  addPlayerToDrawingOrder(newPlayer) {
    if (!this.gameState.drawingOrder.find(p => p.peerId === newPlayer.peerId)) {
      // Insert at random position in the remaining order
      const remainingPositions = this.gameState.drawingOrder.length - this.gameState.currentDrawerIndex;
      if (remainingPositions > 0) {
        const randomPosition = this.gameState.currentDrawerIndex + 1 + Math.floor(Math.random() * remainingPositions);
        this.gameState.drawingOrder.splice(randomPosition, 0, newPlayer);
        console.log('🎲 Added new player to drawing order at position', randomPosition, ':', newPlayer.name);
      } else {
        // Add to end if no remaining positions
        this.gameState.drawingOrder.push(newPlayer);
        console.log('🎲 Added new player to end of drawing order:', newPlayer.name);
      }
    }
  }

  /**
   * Start a new game (only for active/authoritative GameManager)
   */
  startGame(settings) {
    if (!this.isActive) {
      console.warn('❌ GameManager not active, cannot start game');
      return;
    }

    console.log('🎮 Starting new game', {
      isActive: this.isActive,
      settings,
      currentStatus: this.gameState.status
    });

    // Generate new game session
    this.gameState.gameId = `game_${Date.now()}_${this.p2pManager.peerId}`;
    this.gameState.status = 'playing';
    this.gameState.currentRound = 1;
    this.gameState.maxRounds = settings.rounds || 3;
    this.gameState.currentDrawerIndex = 0;
    this.gameState.lastUpdate = Date.now();

    // Create randomized drawing order
    const players = this.p2pManager.getRoomState().players;
    this.gameState.drawingOrder = this.createDrawingOrder(players);

    // Initialize scores for current players
    players.forEach(player => {
      // Check if player has previous score from playerHistory
      const previousData = this.gameState.playerHistory.get(player.peerId);
      const previousScore = previousData?.lastScore || 0;
      this.gameState.scores.set(player.peerId, previousScore);

      // Update player history
      this.gameState.playerHistory.set(player.peerId, {
        name: player.name,
        lastScore: previousScore,
        joinTime: player.joinTime
      });
    });

    console.log('🎮 Game scores initialized:', Object.fromEntries(this.gameState.scores));

    // Clear previous game data
    this.gameState.drawingData = [];
    this.gameState.guesses = [];

    console.log('🎮 Starting new round...');
    this.startNewRound();
  }

  /**
   * Start a new round (only for active/authoritative GameManager)
   */
  startNewRound() {
    if (!this.isActive) {
      console.warn('❌ GameManager not active, cannot start new round');
      return;
    }

    console.log('🎮 Starting new round', this.gameState.currentRound);

    // Check if we have players in drawing order
    if (this.gameState.drawingOrder.length === 0) {
      console.warn('❌ No players in drawing order, cannot start round');
      return;
    }

    // Handle new players that joined mid-game
    const currentPlayers = this.p2pManager.getRoomState().players;
    currentPlayers.forEach(player => {
      if (!this.gameState.drawingOrder.find(p => p.peerId === player.peerId)) {
        this.addPlayerToDrawingOrder(player);

        // Initialize score for new player
        if (!this.gameState.scores.has(player.peerId)) {
          this.gameState.scores.set(player.peerId, 0);
        }

        // Update player history
        this.gameState.playerHistory.set(player.peerId, {
          name: player.name,
          lastScore: this.gameState.scores.get(player.peerId) || 0,
          joinTime: player.joinTime
        });
      }
    });

    // Select next drawer from the randomized order
    this.gameState.currentDrawer = this.gameState.drawingOrder[this.gameState.currentDrawerIndex];
    console.log('🎨 Selected drawer:', this.gameState.currentDrawer.name, 'at index', this.gameState.currentDrawerIndex);

    // Get word count from settings
    const wordCount = this.p2pManager.getRoomState().settings.wordCount || 3;

    // Select random word options for drawer to choose from
    this.gameState.currentWordOptions = getRandomWords(this.words, wordCount);
    this.gameState.currentWord = null; // Will be set when drawer chooses
    console.log('📝 Word options:', this.gameState.currentWordOptions);

    // Start word selection phase
    this.gameState.status = 'wordSelection';
    this.gameState.wordSelectionTimeLeft = 20; // 20 seconds to select word
    this.gameState.lastUpdate = Date.now();

    // Clear previous round data
    this.gameState.drawingData = [];
    this.gameState.guesses = [];

    // Broadcast word selection phase
    console.log('📡 Broadcasting word selection phase...');
    this.broadcastGameState();

    // Start word selection timer
    console.log('⏰ Starting word selection timer...');
    this.startWordSelectionTimer();

    const roundData = {
      round: this.gameState.currentRound,
      drawer: this.gameState.currentDrawer,
      wordOptions: this.gameState.currentWordOptions,
      wordSelectionTimeLeft: this.gameState.wordSelectionTimeLeft,
      status: 'wordSelection'
    };

    console.log('🎮 Emitting wordSelectionStarted event:', roundData);
    this.emit('wordSelectionStarted', roundData);
  }

  /**
   * Start the word selection timer (only for active GameManager)
   */
  startWordSelectionTimer() {
    if (!this.isActive) return;

    if (this.wordSelectionTimer) {
      clearInterval(this.wordSelectionTimer);
    }

    this.wordSelectionTimer = setInterval(() => {
      this.gameState.wordSelectionTimeLeft--;
      this.gameState.lastUpdate = Date.now();

      if (this.gameState.wordSelectionTimeLeft <= 0) {
        // Time's up! Auto-select a random word
        this.autoSelectWord();
      } else {
        this.emit('wordSelectionTimerUpdate', this.gameState.wordSelectionTimeLeft);
        // Broadcast timer updates every second during word selection
        this.broadcastGameState();
      }
    }, 1000);
  }

  /**
   * Auto-select a random word when time runs out
   */
  autoSelectWord() {
    if (!this.isActive) return;

    if (this.wordSelectionTimer) {
      clearInterval(this.wordSelectionTimer);
      this.wordSelectionTimer = null;
    }

    // Select random word from options
    const randomIndex = Math.floor(Math.random() * this.gameState.currentWordOptions.length);
    const selectedWord = this.gameState.currentWordOptions[randomIndex];

    console.log('⏰ Time up! Auto-selecting word:', selectedWord);

    this.gameState.currentWord = selectedWord;
    this.startDrawingPhase();
  }

  /**
   * Start the drawing phase after word selection
   */
  startDrawingPhase() {
    if (!this.isActive) return;

    console.log('🎨 Starting drawing phase with word:', this.gameState.currentWord);

    // Set drawing timer
    const drawTime = this.p2pManager.getRoomState().settings.drawTime || 80;
    this.gameState.timeLeft = drawTime;
    this.gameState.status = 'playing';
    this.gameState.roundStartTime = Date.now();
    this.gameState.lastUpdate = Date.now();

    // Broadcast drawing phase start
    this.broadcastGameState();

    // Start drawing timer
    this.startDrawTimer();

    this.emit('drawingPhaseStarted', {
      round: this.gameState.currentRound,
      drawer: this.gameState.currentDrawer,
      word: this.gameState.currentWord,
      timeLeft: this.gameState.timeLeft
    });
  }

  /**
   * Start the drawing timer (only for active GameManager)
   */
  startDrawTimer() {
    if (!this.isActive) return;

    if (this.drawTimer) {
      clearInterval(this.drawTimer);
    }

    this.drawTimer = setInterval(() => {
      this.gameState.timeLeft--;
      this.gameState.lastUpdate = Date.now();

      if (this.gameState.timeLeft <= 0) {
        this.endRound();
      } else {
        this.emit('timerUpdate', this.gameState.timeLeft);
        // Broadcast timer updates every 5 seconds to keep everyone in sync
        if (this.gameState.timeLeft % 5 === 0) {
          this.broadcastGameState();
        }
      }
    }, 1000);
  }

  /**
   * End the current round (only for active GameManager)
   */
  endRound() {
    if (!this.isActive) return;

    // Clear all timers
    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }
    if (this.wordSelectionTimer) {
      clearInterval(this.wordSelectionTimer);
      this.wordSelectionTimer = null;
    }

    // Update player history with current scores
    this.gameState.scores.forEach((score, peerId) => {
      const playerData = this.gameState.playerHistory.get(peerId);
      if (playerData) {
        playerData.lastScore = score;
      }
    });

    this.gameState.lastUpdate = Date.now();

    this.emit('roundEnded', {
      round: this.gameState.currentRound,
      word: this.gameState.currentWord,
      scores: Object.fromEntries(this.gameState.scores)
    });

    // Broadcast the round end state
    this.broadcastGameState();

    // Move to next drawer in the order
    this.gameState.currentDrawerIndex = (this.gameState.currentDrawerIndex + 1) % this.gameState.drawingOrder.length;

    // Check if game should end
    if (this.gameState.currentRound >= this.gameState.maxRounds && this.gameState.currentDrawerIndex === 0) {
      this.endGame();
    } else {
      // Prepare next round
      if (this.gameState.currentDrawerIndex === 0) {
        this.gameState.currentRound++;
      }

      setTimeout(() => {
        this.startNewRound();
      }, 3000); // 3 second break between rounds
    }
  }

  /**
   * End the game (only for active GameManager)
   */
  endGame() {
    if (!this.isActive) return;

    this.gameState.status = 'finished';
    this.gameState.lastUpdate = Date.now();

    // Calculate final rankings
    const finalScores = Array.from(this.gameState.scores.entries())
      .map(([peerId, score]) => {
        const player = this.p2pManager.getRoomState().players.find(p => p.peerId === peerId);
        return { peerId, player, score };
      })
      .sort((a, b) => b.score - a.score);

    this.broadcastGameState();

    this.emit('gameEnded', {
      finalScores,
      winner: finalScores[0]
    });
  }

  /**
   * Handle a player's guess (only for active GameManager)
   */
  submitGuess(peerId, guess) {
    if (!this.isActive) return;
    if (this.gameState.status !== 'playing') return;
    if (peerId === this.gameState.currentDrawer?.peerId) return; // Drawer can't guess

    const player = this.p2pManager.getRoomState().players.find(p => p.peerId === peerId);
    if (!player) return;

    const guessData = {
      peerId,
      playerName: player.name,
      guess: guess.trim().toLowerCase(),
      timestamp: Date.now(),
      isCorrect: false
    };

    // Check if guess is correct
    if (this.gameState.currentWord &&
        guessData.guess === this.gameState.currentWord.toLowerCase()) {
      guessData.isCorrect = true;

      // Award points
      const timeBonus = Math.max(0, this.gameState.timeLeft);
      const points = 100 + timeBonus;

      const currentScore = this.gameState.scores.get(peerId) || 0;
      this.gameState.scores.set(peerId, currentScore + points);

      // Award points to drawer too
      const drawerScore = this.gameState.scores.get(this.gameState.currentDrawer.peerId) || 0;
      this.gameState.scores.set(this.gameState.currentDrawer.peerId, drawerScore + 50);

      this.emit('correctGuess', { player, points, timeBonus });

      // End round early on correct guess
      setTimeout(() => {
        this.endRound();
      }, 2000);
    }

    this.gameState.guesses.push(guessData);
    this.gameState.lastUpdate = Date.now();
    this.emit('newGuess', guessData);

    // Broadcast updated game state
    this.broadcastGameState();
  }

  /**
   * Handle word selection by drawer (only for active GameManager)
   */
  selectWord(peerId, selectedWord) {
    if (!this.isActive) return;
    if (this.gameState.status !== 'wordSelection') return; // Only during word selection phase
    if (peerId !== this.gameState.currentDrawer?.peerId) return; // Only drawer can select
    if (!this.gameState.currentWordOptions.includes(selectedWord)) return; // Must be valid option

    console.log('📝 Word selected by drawer:', selectedWord);

    // Stop word selection timer
    if (this.wordSelectionTimer) {
      clearInterval(this.wordSelectionTimer);
      this.wordSelectionTimer = null;
    }

    this.gameState.currentWord = selectedWord;
    this.gameState.lastUpdate = Date.now();

    this.emit('wordSelected', { word: selectedWord, drawer: this.gameState.currentDrawer });

    // Start drawing phase
    this.startDrawingPhase();
  }

  /**
   * Handle drawing data from the current drawer (only for active GameManager)
   */
  handleDrawingData(drawingData) {
    if (!this.isActive) return;
    if (this.gameState.status !== 'playing') return;

    this.gameState.drawingData.push({
      ...drawingData,
      timestamp: Date.now()
    });

    this.gameState.lastUpdate = Date.now();
    this.emit('drawingUpdate', drawingData);

    // Broadcast drawing data to all peers
    this.broadcastGameState();
  }

  /**
   * Handle player disconnection (only for active GameManager)
   */
  handlePlayerDisconnection(peerId) {
    if (!this.isActive) return;

    console.log('🎮 Handling player disconnection:', peerId);

    // Save player's score to history before they leave
    const currentScore = this.gameState.scores.get(peerId) || 0;
    const player = this.gameState.drawingOrder.find(p => p.peerId === peerId);
    if (player) {
      this.gameState.playerHistory.set(peerId, {
        name: player.name,
        lastScore: currentScore,
        joinTime: player.joinTime
      });
    }

    // If the current drawer disconnects, end the round
    if (this.gameState.currentDrawer?.peerId === peerId &&
        (this.gameState.status === 'playing' || this.gameState.status === 'wordSelection')) {
      console.log('🎮 Current drawer disconnected, ending round');
      this.endRound();
    }

    // Remove player from drawing order
    this.gameState.drawingOrder = this.gameState.drawingOrder.filter(p => p.peerId !== peerId);

    // Adjust current drawer index if needed
    if (this.gameState.drawingOrder.length > 0) {
      this.gameState.currentDrawerIndex = this.gameState.currentDrawerIndex % this.gameState.drawingOrder.length;
    }

    // Keep score in case they rejoin
    // this.gameState.scores.delete(peerId); // Don't delete, keep for potential rejoin
    this.gameState.lastUpdate = Date.now();

    this.emit('playerDisconnected', peerId);

    // Broadcast updated game state
    this.broadcastGameState();
  }

  /**
   * Broadcast current game state to all peers (only for active GameManager)
   */
  broadcastGameState() {
    if (!this.isActive) return;

    // Convert Map to Object for serialization
    const gameStateForBroadcast = {
      ...this.gameState,
      scores: Object.fromEntries(this.gameState.scores)
    };

    this.p2pManager.broadcastGameState(gameStateForBroadcast);
  }

  /**
   * Get current game state
   */
  getGameState() {
    return {
      ...this.gameState,
      scores: Object.fromEntries(this.gameState.scores)
    };
  }

  /**
   * Get player scores
   */
  getScores() {
    return Object.fromEntries(this.gameState.scores);
  }

  /**
   * Check if current user is the drawer
   */
  isCurrentDrawer() {
    return this.gameState.currentDrawer?.peerId === this.p2pManager.peerId;
  }

  /**
   * Check if this GameManager is active/authoritative
   */
  isActiveManager() {
    return this.isActive;
  }

  /**
   * Reset game to waiting state (only for active GameManager)
   */
  resetGame() {
    if (!this.isActive) return;

    console.log('🎮 Resetting game to waiting state');

    // Stop any running timers
    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }
    if (this.wordSelectionTimer) {
      clearInterval(this.wordSelectionTimer);
      this.wordSelectionTimer = null;
    }

    // Reset game state
    this.gameState.status = 'waiting';
    this.gameState.currentRound = 1;
    this.gameState.currentDrawerIndex = 0;
    this.gameState.drawingOrder = [];
    this.gameState.currentDrawer = null;
    this.gameState.currentWord = null;
    this.gameState.currentWordOptions = [];
    this.gameState.wordSelectionTimeLeft = 0;
    this.gameState.timeLeft = 0;
    this.gameState.drawingData = [];
    this.gameState.guesses = [];
    this.gameState.lastUpdate = Date.now();

    // Keep scores and player history for potential future games
    // this.gameState.scores.clear();
    // this.gameState.playerHistory.clear();

    this.broadcastGameState();
    this.emit('gameReset');
  }

  /**
   * Cleanup
   */
  cleanup() {
    console.log('🎮 Cleaning up GameManager');

    if (this.drawTimer) {
      clearInterval(this.drawTimer);
      this.drawTimer = null;
    }
    if (this.wordSelectionTimer) {
      clearInterval(this.wordSelectionTimer);
      this.wordSelectionTimer = null;
    }

    this.isActive = false;
    this.removeAllListeners();
  }
}

export default GameManager;
